<?php
/**
 * Localization.php
 * Класс для работы с локализацией
 */

class Localization {
    private static $instance = null;
    private $currentLanguage = 'ru';
    private $translations = [];
    private $fallbackLanguage = 'en';

    // Русскоязычные страны и их коды
    private $russianSpeakingCountries = [
        'RU', // Россия
        'BY', // Беларусь
        'KZ', // Казахстан
        'KG', // Киргизия
        'TJ', // Таджикистан
        'UZ', // Узбекистан
        'AM', // Армения
        'AZ', // Азербайджан
        'GE', // Грузия
        'MD', // Молдова
        'UA'  // Украина
    ];

    private function __construct() {
        $this->loadTranslations();
    }

    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Загружает переводы для всех языков
     */
    private function loadTranslations() {
        $localesDir = __DIR__ . '/../locales/';

        // Загружаем русский язык
        $ruFile = $localesDir . 'ru.json';
        if (file_exists($ruFile)) {
            $this->translations['ru'] = json_decode(file_get_contents($ruFile), true);
        }

        // Загружаем английский язык
        $enFile = $localesDir . 'en.json';
        if (file_exists($enFile)) {
            $this->translations['en'] = json_decode(file_get_contents($enFile), true);
        }
    }

    /**
     * Определяет язык пользователя по данным Telegram
     */
    public function detectLanguage($telegramUser) {
        // Список русскоязычных кодов языков
        $russianLanguageCodes = ['ru', 'be', 'uk', 'kk', 'ky', 'tg', 'uz', 'hy', 'az', 'ka', 'ro'];

        // По умолчанию английский
        $detectedLanguage = 'en';

        // Проверяем язык интерфейса Telegram
        if (isset($telegramUser['language_code'])) {
            $langCode = strtolower($telegramUser['language_code']);

            // Если язык входит в список русскоязычных - ставим русский
            if (in_array($langCode, $russianLanguageCodes)) {
                $detectedLanguage = 'ru';
            }
        }

        // Если язык еще не определен как русский, проверяем страну
        if ($detectedLanguage === 'en' && isset($telegramUser['country_code'])) {
            $countryCode = strtoupper($telegramUser['country_code']);
            if (in_array($countryCode, $this->russianSpeakingCountries)) {
                $detectedLanguage = 'ru';
            }
        }

        return $detectedLanguage;
    }

    /**
     * Устанавливает текущий язык
     */
    public function setLanguage($language) {
        if (isset($this->translations[$language])) {
            $this->currentLanguage = $language;
        }
    }

    /**
     * Получает текущий язык
     */
    public function getCurrentLanguage() {
        return $this->currentLanguage;
    }

    /**
     * Получает перевод по ключу
     */
    public function get($key, $params = []) {
        $keys = explode('.', $key);
        $translation = $this->translations[$this->currentLanguage] ?? [];

        // Ищем перевод по ключу
        foreach ($keys as $k) {
            if (isset($translation[$k])) {
                $translation = $translation[$k];
            } else {
                // Если не найден, пробуем fallback язык
                $translation = $this->translations[$this->fallbackLanguage] ?? [];
                foreach ($keys as $fallbackKey) {
                    if (isset($translation[$fallbackKey])) {
                        $translation = $translation[$fallbackKey];
                    } else {
                        return $key; // Возвращаем ключ, если перевод не найден
                    }
                }
                break;
            }
        }

        // Если это не строка, возвращаем ключ
        if (!is_string($translation)) {
            return $key;
        }

        // Заменяем параметры в строке
        foreach ($params as $param => $value) {
            $translation = str_replace('{' . $param . '}', $value, $translation);
        }

        return $translation;
    }

    /**
     * Определяет язык по стране из Telegram Web App
     */
    public function detectLanguageFromWebApp($initData) {
        // Парсим initData для получения информации о пользователе
        parse_str($initData, $data);

        if (isset($data['user'])) {
            $user = json_decode($data['user'], true);
            return $this->detectLanguage($user);
        }

        return 'en';
    }

    /**
     * Проверяет, является ли страна русскоязычной
     */
    public function isRussianSpeakingCountry($countryCode) {
        return in_array(strtoupper($countryCode), $this->russianSpeakingCountries);
    }
}
?>
