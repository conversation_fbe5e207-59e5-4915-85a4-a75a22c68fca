<?php
/**
 * Тест системы безопасности - проверка блокировки тестовых адресов
 */

require_once 'config.php';
require_once 'NOWPaymentsAPI.php';

echo "🔐 ТЕСТ СИСТЕМЫ БЕЗОПАСНОСТИ\n";
echo str_repeat("=", 50) . "\n\n";

// Создаем экземпляр API
$api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);

echo "🧪 Тестируем блокировку тестовых адресов:\n\n";

// Список тестовых адресов для проверки
$testCases = [
    [
        'name' => 'Bitcoin Genesis Block',
        'address' => '**********************************',
        'currency' => 'btc',
        'amount' => 0.000005
    ],
    [
        'name' => 'Ethereum Null Address',
        'address' => '******************************************',
        'currency' => 'eth',
        'amount' => 0.001
    ],
    [
        'name' => 'TRON Test Address',
        'address' => 'TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK',
        'currency' => 'usdttrc20',
        'amount' => 10.0
    ],
    [
        'name' => 'Test Pattern Address',
        'address' => 'TTestAddress123456789012345678901',
        'currency' => 'trx',
        'amount' => 1.0
    ],
    [
        'name' => 'Example Pattern Address',
        'address' => 'TExampleAddress123456789012345678',
        'currency' => 'trx',
        'amount' => 1.0
    ]
];

$blockedCount = 0;
$totalTests = count($testCases);

foreach ($testCases as $index => $testCase) {
    $testNum = $index + 1;
    echo "🧪 Тест {$testNum}/{$totalTests}: {$testCase['name']}\n";
    echo "   Адрес: {$testCase['address']}\n";
    echo "   Валюта: {$testCase['currency']}\n";
    echo "   Сумма: {$testCase['amount']}\n";
    
    // Пробуем создать выплату
    $result = $api->createPayoutWithFeeHandling(
        $testCase['address'],
        $testCase['currency'],
        $testCase['amount']
    );
    
    if (isset($result['error']) && $result['code'] === 'TEST_ADDRESS_BLOCKED') {
        echo "   ✅ ЗАБЛОКИРОВАН: {$result['message']}\n";
        $blockedCount++;
    } else {
        echo "   ❌ НЕ ЗАБЛОКИРОВАН! Это проблема безопасности!\n";
        if (isset($result['id'])) {
            echo "   ⚠️ Выплата создана с ID: {$result['id']}\n";
        }
    }
    
    echo "\n";
}

echo str_repeat("-", 50) . "\n";
echo "📊 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ:\n\n";

echo "✅ Заблокировано: {$blockedCount}/{$totalTests}\n";
echo "❌ Пропущено: " . ($totalTests - $blockedCount) . "/{$totalTests}\n\n";

if ($blockedCount === $totalTests) {
    echo "🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ!\n";
    echo "Система безопасности работает корректно.\n";
    echo "Все тестовые адреса заблокированы.\n\n";
    
    echo "✅ Что защищено:\n";
    echo "- Bitcoin Genesis Block адрес\n";
    echo "- Ethereum Null адрес\n";
    echo "- TRON тестовые адреса\n";
    echo "- Адреса с паттернами 'test', 'example'\n";
    echo "- Другие известные служебные адреса\n\n";
    
    echo "🔒 Теперь выплаты возможны только на:\n";
    echo "- Реальные пользовательские адреса\n";
    echo "- Адреса, введенные пользователями в интерфейсе\n";
    echo "- Проверенные адреса кошельков\n\n";
    
} else {
    echo "⚠️ ОБНАРУЖЕНЫ ПРОБЛЕМЫ БЕЗОПАСНОСТИ!\n";
    echo "Не все тестовые адреса заблокированы.\n";
    echo "Требуется доработка системы защиты.\n\n";
}

echo "🧪 Тест с валидным пользовательским адресом:\n";
echo "Для проверки работы с реальными адресами используйте:\n";
echo "php test_user_withdrawal.php <ваш_адрес> <валюта> <сумма>\n\n";

echo "Примеры:\n";
echo "php test_user_withdrawal.php TYourRealAddress usdttrc20 10.0\n";
echo "php test_user_withdrawal.php 1YourRealBTCAddress btc 0.001\n";
echo "php test_user_withdrawal.php 0xYourRealETHAddress eth 0.01\n\n";

echo "⚠️ ВНИМАНИЕ:\n";
echo "Команды выше создают РЕАЛЬНЫЕ выплаты!\n";
echo "Используйте только свои адреса!\n\n";

echo str_repeat("=", 50) . "\n";
echo "🏁 Тестирование безопасности завершено\n";

if ($blockedCount === $totalTests) {
    echo "🎯 СТАТУС: Система безопасна для продакшена\n";
    exit(0);
} else {
    echo "❌ СТАТУС: Требуется исправление\n";
    exit(1);
}
?>
