/* 🔥 UNIFIED CYBERPUNK DESIGN SYSTEM 🔥 */
/* Единый файл стилей без конфликтов */

/* === CYBERPUNK COLOR PALETTE === */
:root {
  /* Neon Colors */
  --cyber-neon-blue: #00ffff;
  --cyber-neon-pink: #ff0080;
  --cyber-neon-green: #00ff41;
  --cyber-neon-purple: #8a2be2;
  --cyber-neon-orange: #ff6600;
  --cyber-neon-yellow: #ffff00;
  
  /* Dark Backgrounds */
  --cyber-bg-primary: #0a0a0a;
  --cyber-bg-secondary: #1a1a2e;
  --cyber-bg-tertiary: #16213e;
  --cyber-bg-card: #0f0f23;
  
  /* Gradients */
  --cyber-gradient-main: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
  --cyber-gradient-neon: linear-gradient(45deg, #00ffff, #ff0080, #00ff41);
  --cyber-gradient-card: linear-gradient(145deg, #0f0f23 0%, #1a1a2e 100%);
  
  /* Text Colors */
  --cyber-text-primary: #ffffff;
  --cyber-text-secondary: #b0b0b0;
  --cyber-text-accent: #00ffff;
  --cyber-text-warning: #ff6600;
  --cyber-text-success: #00ff41;
  --cyber-text-error: #ff0080;
  
  /* Shadows & Glows */
  --cyber-glow-blue: 0 0 20px rgba(0, 255, 255, 0.5);
  --cyber-glow-pink: 0 0 20px rgba(255, 0, 128, 0.5);
  --cyber-glow-green: 0 0 20px rgba(0, 255, 65, 0.5);
  --cyber-glow-purple: 0 0 20px rgba(138, 43, 226, 0.5);
  
  /* Borders */
  --cyber-border-neon: 2px solid var(--cyber-neon-blue);
  --cyber-border-glow: 1px solid rgba(0, 255, 255, 0.3);
  
  /* Transition */
  --page-transition-duration: 0.3s;
}

/* === CYBERPUNK FONTS === */
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap');

/* === GLOBAL STYLES === */
*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  height: 100%;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Rajdhani', sans-serif;
  background: var(--cyber-bg-primary);
  background-image: 
    radial-gradient(circle at 20% 80%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 0, 128, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(0, 255, 65, 0.05) 0%, transparent 50%);
  color: var(--cyber-text-primary);
  height: 100vh;
  overflow: hidden;
  overscroll-behavior: none;
}

/* === CYBERPUNK CONTAINER === */
.app-container {
  background: var(--cyber-gradient-main);
  height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  padding: 0;
}

.app-container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    linear-gradient(90deg, transparent 0%, rgba(0, 255, 255, 0.03) 50%, transparent 100%),
    linear-gradient(0deg, transparent 0%, rgba(255, 0, 128, 0.03) 50%, transparent 100%);
  pointer-events: none;
  z-index: 1;
}

/* === CYBERPUNK SCAN LINE EFFECT === */
.cyber-scan-line {
  position: fixed;
  top: 0;
  left: -2px;
  width: 2px;
  height: 100vh;
  background: linear-gradient(to bottom, 
    transparent 0%, 
    var(--cyber-neon-blue) 50%, 
    transparent 100%);
  animation: cyber-scan 8s linear infinite;
  z-index: 1000;
  pointer-events: none;
}

/* === CYBERPUNK HEADER === */
.app-header {
  flex-shrink: 0;
  z-index: 10;
  background: var(--cyber-gradient-card);
  border-bottom: var(--cyber-border-glow);
  padding: 12px 20px;
  backdrop-filter: blur(15px);
  box-shadow: 
    0 2px 20px rgba(0, 0, 0, 0.5),
    var(--cyber-glow-blue);
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 60px;
}

.app-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--cyber-gradient-neon);
  opacity: 0.8;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  min-width: 0;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--cyber-gradient-neon);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--cyber-glow-blue);
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
  border: 2px solid var(--cyber-neon-blue);
}

.user-avatar::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(0, 255, 255, 0.1) 0%, transparent 70%);
  animation: cyber-pulse 3s ease-in-out infinite;
}

.user-avatar img,
.user-avatar-icon {
  width: 24px;
  height: 24px;
  filter: brightness(0) invert(1);
  z-index: 1;
  border-radius: 0;
  object-fit: contain;
}

.user-name {
  font-family: 'Orbitron', monospace;
  font-weight: 600;
  color: var(--cyber-text-primary);
  text-transform: uppercase;
  letter-spacing: 1px;
  text-shadow: var(--cyber-glow-blue);
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;
}

.balance-info {
  display: flex;
  align-items: center;
  gap: 6px;
  background: rgba(0, 0, 0, 0.7);
  border: var(--cyber-border-glow);
  border-radius: 20px;
  padding: 8px 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  flex-shrink: 0;
}

.balance-info:hover {
  border-color: var(--cyber-neon-blue);
  box-shadow: var(--cyber-glow-blue);
  transform: translateY(-2px);
}

.balance-amount {
  font-family: 'Orbitron', monospace;
  font-weight: 700;
  color: var(--cyber-neon-green);
  text-shadow: var(--cyber-glow-green);
  font-size: 16px;
  line-height: 1;
}

.balance-currency {
  font-family: 'Orbitron', monospace;
  font-size: 11px;
  color: var(--cyber-text-secondary);
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 500;
}

.clickable-balance {
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.clickable-balance:hover {
  opacity: 0.8;
}

/* === CYBERPUNK SECTIONS === */
.app-main,
.app-section {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 20px;
  padding-bottom: 100px;
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 15px;
  width: 100%;
  background-color: transparent;
  opacity: 1;
  transform: translateX(0);
  transition: opacity var(--page-transition-duration) ease-in-out,
    transform var(--page-transition-duration) ease-in-out;
  will-change: opacity, transform;
  z-index: 2;
}

.app-main.active-section,
.app-section.active-section {
  z-index: 3;
}

/* Скроллбар */
.app-main::-webkit-scrollbar,
.app-section::-webkit-scrollbar {
  width: 6px;
}

.app-main::-webkit-scrollbar-track,
.app-section::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 3px;
}

.app-main::-webkit-scrollbar-thumb,
.app-section::-webkit-scrollbar-thumb {
  background: var(--cyber-neon-blue);
  border-radius: 3px;
  box-shadow: var(--cyber-glow-blue);
}

.app-main::-webkit-scrollbar-thumb:hover,
.app-section::-webkit-scrollbar-thumb:hover {
  background: var(--cyber-neon-pink);
  box-shadow: var(--cyber-glow-pink);
}

/* Анимации переходов */
.page-hidden {
  display: none;
  opacity: 0;
  pointer-events: none;
}

.page-enter {
  opacity: 0;
  transform: translateX(20px);
  z-index: 3;
}

.page-enter-active {
  opacity: 1;
  transform: translateX(0);
  z-index: 3;
}

.page-leave-active {
  opacity: 0;
  transform: translateX(-20px);
  z-index: 1;
  pointer-events: none;
}

/* === CYBERPUNK HEADERS === */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Orbitron', monospace;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 2px;
  margin: 0 0 20px 0;
  position: relative;
}

h1 {
  font-size: 2.5rem;
  background: var(--cyber-gradient-neon);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: var(--cyber-glow-blue);
}

h2 {
  font-size: 2rem;
  color: var(--cyber-neon-blue);
  text-shadow: var(--cyber-glow-blue);
  text-align: center;
  margin-top: 0;
  margin-bottom: 10px;
  font-weight: 600;
  font-size: 20px;
}

h3 {
  font-size: 1.5rem;
  color: var(--cyber-neon-pink);
  text-shadow: var(--cyber-glow-pink);
  margin-top: 0;
  margin-bottom: 8px;
  font-size: 16px;
  font-weight: 600;
}

/* === STATUS MESSAGE === */
.status-message {
  padding: 15px;
  font-size: 14px;
  text-align: center;
  min-height: 20px;
  border-radius: 10px;
  word-wrap: break-word;
  margin-bottom: 15px;
  background: var(--cyber-gradient-card);
  border: var(--cyber-border-glow);
  color: var(--cyber-text-primary);
  font-family: 'Orbitron', monospace;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s ease;
  opacity: 1;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  box-shadow: var(--cyber-glow-blue);
}

.status-message::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.1), transparent);
  animation: cyber-scan 2s linear infinite;
}

.status-message:empty {
  opacity: 0;
  padding: 0;
  min-height: 0;
  margin-bottom: 0;
}

.status-message.success {
  color: var(--cyber-neon-green);
  text-shadow: var(--cyber-glow-green);
  border-color: var(--cyber-neon-green);
  box-shadow: var(--cyber-glow-green);
}

.status-message.error {
  color: var(--cyber-neon-pink);
  text-shadow: var(--cyber-glow-pink);
  border-color: var(--cyber-neon-pink);
  box-shadow: var(--cyber-glow-pink);
}

.status-message.info {
  color: var(--cyber-neon-blue);
  text-shadow: var(--cyber-glow-blue);
  border-color: var(--cyber-neon-blue);
  box-shadow: var(--cyber-glow-blue);
}

/* === CYBERPUNK CARDS === */
.earn-block,
.friends-block {
  background: var(--cyber-gradient-card);
  border: var(--cyber-border-glow);
  border-radius: 15px;
  padding: 25px;
  margin: 20px 0;
  position: relative;
  overflow: hidden;
  box-shadow:
    var(--cyber-glow-blue),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  z-index: 2;
}

.earn-block::before,
.friends-block::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--cyber-gradient-neon);
  opacity: 0.8;
}

.earn-block::after,
.friends-block::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background:
    radial-gradient(circle, rgba(0, 255, 255, 0.05) 0%, transparent 70%);
  animation: cyber-pulse 4s ease-in-out infinite;
  pointer-events: none;
}

.friends-block p {
  font-size: 14px;
  color: var(--cyber-text-primary);
  margin-top: 0;
  margin-bottom: 12px;
  line-height: 1.4;
}

.friends-block p.hint {
  font-size: 12px;
  font-style: italic;
  color: var(--cyber-text-secondary);
}

/* === CYBERPUNK BUTTONS === */
.action-button {
  width: 100%;
  padding: 15px 25px;
  font-size: 16px;
  font-weight: 600;
  border: var(--cyber-border-glow);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  appearance: none;
  -webkit-appearance: none;
  text-align: center;
  font-family: 'Orbitron', monospace;
  text-transform: uppercase;
  letter-spacing: 1px;
  background: var(--cyber-gradient-card);
  color: var(--cyber-text-primary);
  position: relative;
  overflow: hidden;
  box-shadow: var(--cyber-glow-blue);
}

.action-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.action-button:hover::before {
  left: 100%;
}

.action-button:hover {
  border-color: var(--cyber-neon-blue);
  box-shadow: var(--cyber-glow-blue);
  transform: translateY(-2px);
}

.action-button:active {
  transform: translateY(0);
}

.action-button.primary-action {
  background: linear-gradient(135deg, var(--cyber-neon-blue), var(--cyber-neon-purple));
  border-color: var(--cyber-neon-blue);
  color: var(--cyber-bg-primary);
  font-weight: 700;
}

.action-button.secondary-action {
  background: linear-gradient(135deg, var(--cyber-neon-pink), var(--cyber-neon-orange));
  border-color: var(--cyber-neon-pink);
  color: var(--cyber-bg-primary);
}

.action-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: rgba(85, 85, 85, 0.5);
  color: var(--cyber-text-secondary);
  box-shadow: none;
  transform: none;
  border-color: rgba(85, 85, 85, 0.5);
}

/* Эффект нажатой кнопки */
.action-button.pressed {
  transform: translateY(2px);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  opacity: 0.8;
}

/* Счетчик обратного отсчета */
.countdown-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: bold;
  color: var(--cyber-neon-blue);
  text-shadow: var(--cyber-glow-blue);
  border-radius: 12px;
  z-index: 10;
  font-family: 'Orbitron', monospace;
  backdrop-filter: blur(5px);
}

/* Цвета для разных типов кнопок */
.action-button.purple-button {
  background: linear-gradient(135deg, #8b5cf6, #a855f7);
  border-color: #8b5cf6;
}

.action-button.orange-button {
  background: linear-gradient(135deg, #ff6b35, #ff8c42);
  border-color: #ff6b35;
}

.action-button.blue-button {
  background: linear-gradient(135deg, #3b82f6, #60a5fa);
  border-color: #3b82f6;
}

.action-button.red-button {
  background: linear-gradient(135deg, #ef4444, #f87171);
  border-color: #ef4444;
}

.action-button.yellow-button {
  background: linear-gradient(135deg, #eab308, #fbbf24);
  border-color: #eab308;
  color: #000000;
}

/* === CYBERPUNK NAVIGATION === */
.app-nav {
  background: var(--cyber-gradient-card);
  border-top: var(--cyber-border-glow);
  backdrop-filter: blur(20px);
  box-shadow:
    0 -5px 20px rgba(0, 0, 0, 0.5),
    var(--cyber-glow-blue);
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
  flex-shrink: 0;
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 10px 0;
  min-height: 70px;
}

.app-nav::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--cyber-gradient-neon);
  opacity: 0.8;
}

.nav-button {
  background: transparent;
  border: none;
  padding: 15px;
  border-radius: 0;
  box-shadow: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1;
  max-width: 120px;
  color: var(--cyber-text-secondary);
  font-family: 'Orbitron', monospace;
}

.nav-button.active {
  background: linear-gradient(135deg, rgba(0, 255, 255, 0.2), rgba(255, 0, 128, 0.2));
  box-shadow: inset var(--cyber-glow-blue);
  color: var(--cyber-text-primary);
  border-radius: 10px;
  margin: 0 5px;
}

.nav-button:hover {
  color: var(--cyber-text-primary);
  transform: translateY(-2px);
}

.nav-icon {
  width: 24px;
  height: 24px;
  fill: currentColor;
  filter: drop-shadow(0 0 5px currentColor);
  transition: all 0.3s ease;
}

.nav-text {
  color: inherit;
  font-weight: 600;
  text-shadow: 0 0 5px currentColor;
  font-size: 11px;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s ease;
}

/* === CYBERPUNK INPUTS === */
input, select, textarea {
  font-family: 'Rajdhani', sans-serif;
  background: rgba(0, 0, 0, 0.7);
  border: var(--cyber-border-glow);
  border-radius: 8px;
  padding: 15px;
  color: var(--cyber-text-primary);
  font-size: 16px;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
}

input:focus, select:focus, textarea:focus {
  outline: none;
  border-color: var(--cyber-neon-blue);
  box-shadow: var(--cyber-glow-blue);
  background: rgba(0, 0, 0, 0.9);
}

input::placeholder {
  color: var(--cyber-text-secondary);
  font-style: italic;
}

/* === REFERRAL AREA === */
.referral-link-area {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 10px;
}

.referral-link-area input[type="text"] {
  flex-grow: 1;
  padding: 12px 15px;
  border: var(--cyber-border-glow);
  background: rgba(0, 0, 0, 0.7);
  color: var(--cyber-text-primary);
  border-radius: 10px;
  font-size: 14px;
  font-family: 'Rajdhani', sans-serif;
}

.referral-link-area .copy-button {
  padding: 0;
  background: var(--cyber-gradient-card);
  border: var(--cyber-border-glow);
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 45px;
  height: 45px;
  flex-shrink: 0;
  box-shadow: var(--cyber-glow-blue);
}

.referral-link-area .copy-button:hover {
  border-color: var(--cyber-neon-blue);
  box-shadow: var(--cyber-glow-blue);
  transform: translateY(-2px);
}

.referral-link-area .copy-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: rgba(85, 85, 85, 0.5);
  border-color: rgba(85, 85, 85, 0.5);
  box-shadow: none;
  transform: none;
}

/* === CYBERPUNK ANIMATIONS === */
@keyframes cyber-flicker {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

@keyframes cyber-scan {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100vw); }
}

@keyframes cyber-glow-pulse {
  0%, 100% {
    box-shadow: var(--cyber-glow-blue);
  }
  50% {
    box-shadow:
      var(--cyber-glow-blue),
      0 0 30px rgba(0, 255, 255, 0.8);
  }
}

@keyframes cyber-pulse {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.1;
    transform: scale(1.1);
  }
}

/* === RESPONSIVE === */
@media (max-width: 768px) {
  .app-header {
    padding: 10px 15px;
    min-height: 55px;
  }

  .user-avatar {
    width: 35px;
    height: 35px;
  }

  .user-avatar img,
  .user-avatar-icon {
    width: 20px;
    height: 20px;
  }

  .user-name {
    font-size: 12px;
    max-width: 120px;
  }

  .balance-amount {
    font-size: 14px;
  }

  .balance-currency {
    font-size: 10px;
  }

  .app-main,
  .app-section {
    padding: 15px;
    padding-bottom: 90px;
  }

  .nav-button {
    padding: 12px;
  }

  .nav-icon {
    width: 20px;
    height: 20px;
  }

  .nav-text {
    font-size: 10px;
  }
}
