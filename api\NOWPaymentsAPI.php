<?php
/**
 * NOWPaymentsAPI.php
 * Класс для работы с NOWPayments API
 */

class NOWPaymentsAPI {
    private $apiKey;
    private $publicKey;
    private $ipnSecret;
    private $apiUrl;
    private $jwtToken;

    public function __construct($apiKey, $publicKey, $ipnSecret, $apiUrl) {
        $this->apiKey = $apiKey;        // Приватный ключ для payout
        $this->publicKey = $publicKey;  // Публичный ключ для estimate
        $this->ipnSecret = $ipnSecret;
        $this->apiUrl = $apiUrl;
        $this->jwtToken = null;
    }

    /**
     * Получить JWT токен для авторизации через email и password
     */
    public function getJWTToken($email = null, $password = null) {
        if ($this->jwtToken) {
            return $this->jwtToken;
        }

        // Используем переданные данные или константы из конфига
        $authEmail = $email ?: (defined('NOWPAYMENTS_EMAIL') ? NOWPAYMENTS_EMAIL : null);
        $authPassword = $password ?: (defined('NOWPAYMENTS_PASSWORD') ? NOWPAYMENTS_PASSWORD : null);

        if (!$authEmail || !$authPassword) {
            error_log("NOWPaymentsAPI ERROR: Email и password не указаны для получения JWT токена");
            return false;
        }

        $url = $this->apiUrl . '/auth';
        $data = [
            'email' => $authEmail,
            'password' => $authPassword
        ];

        error_log("NOWPaymentsAPI INFO: Попытка получения JWT токена для email: " . $authEmail);

        // Для получения JWT токена не нужна авторизация
        $response = $this->makeRequestWithoutAuth('POST', $url, $data);

        if ($response && isset($response['token'])) {
            $this->jwtToken = $response['token'];
            error_log("NOWPaymentsAPI SUCCESS: JWT токен получен успешно");
            return $this->jwtToken;
        }

        error_log("NOWPaymentsAPI ERROR: Не удалось получить JWT токен");
        if ($response) {
            error_log("NOWPaymentsAPI ERROR: Ответ сервера: " . json_encode($response));
        }
        return false;
    }

    /**
     * Получить оценку суммы для конвертации
     */
    public function getEstimateAmount($amount, $fromCurrency, $toCurrency) {
        $url = $this->apiUrl . '/estimate';
        $params = [
            'amount' => $amount,
            'currency_from' => $fromCurrency,
            'currency_to' => $toCurrency
        ];

        // Для estimate пробуем оба ключа
        $response = $this->makeRequest('GET', $url . '?' . http_build_query($params), null, 'x-api-key', false);

        if ($response === false) {
            // Если приватный ключ не сработал, пробуем публичный
            $response = $this->makeRequest('GET', $url . '?' . http_build_query($params), null, 'x-api-key', true);
        }

        if ($response === false) {
            error_log("NOWPaymentsAPI ERROR: Не удалось получить оценку суммы");
            return false;
        }

        return $response;
    }

    /**
     * Создать массовую выплату с автоматическим переключением методов авторизации
     */
    public function createMassWithdrawal($withdrawals) {
        // Пробуем разные endpoints и методы авторизации
        $endpoints = [
            ['url' => '/mass-payouts', 'data_key' => 'payouts'],
            ['url' => '/payout', 'data_key' => 'withdrawals']
        ];

        $authMethods = ['both', 'bearer', 'x-api-key'];

        foreach ($endpoints as $endpoint) {
            $url = $this->apiUrl . $endpoint['url'];
            $data = [$endpoint['data_key'] => $withdrawals];

            error_log("NOWPaymentsAPI INFO: Пробуем endpoint: " . $url);
            error_log("NOWPaymentsAPI INFO: Данные запроса: " . json_encode($data));

            foreach ($authMethods as $authMethod) {
                error_log("NOWPaymentsAPI INFO: Пробуем метод авторизации: " . $authMethod);

                $response = $this->makeRequest('POST', $url, $data, $authMethod);

                if ($response !== false) {
                    error_log("NOWPaymentsAPI SUCCESS: Массовая выплата создана с endpoint: " . $url . " и методом авторизации: " . $authMethod);
                    error_log("NOWPaymentsAPI INFO: Ответ API: " . json_encode($response));
                    return $response;
                }

                error_log("NOWPaymentsAPI INFO: Комбинация " . $url . " + " . $authMethod . " не сработала");
            }
        }

        error_log("NOWPaymentsAPI ERROR: Все комбинации endpoints и методов авторизации не сработали");
        return false;
    }

    /**
     * Создать одиночную выплату с автоматическим переключением методов авторизации
     */
    public function createSinglePayout($address, $currency, $amount) {
        // Используем только рабочий endpoint для массовых выплат
        $url = $this->apiUrl . '/payout';

        // Структура данных для массовых выплат (единственный рабочий endpoint)
        $data = [
            'withdrawals' => [
                [
                    'address' => $address,
                    'currency' => $currency,
                    'amount' => $amount,
                    'ipn_callback_url' => 'https://app.uniqpaid.com/test2/api/withdrawal_callback.php'
                ]
            ]
        ];

        error_log("NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint");
        error_log("NOWPaymentsAPI INFO: Данные запроса: " . json_encode($data));

        // Пробуем только рабочий метод авторизации (both)
        $response = $this->makeRequest('POST', $url, $data, 'both');

        if ($response !== false && !isset($response['error'])) {
            error_log("NOWPaymentsAPI SUCCESS: Выплата создана успешно");
            error_log("NOWPaymentsAPI INFO: Ответ API: " . json_encode($response));
            return $response;
        }

        // Если есть детальная ошибка, возвращаем её
        if (isset($response['error'])) {
            error_log("NOWPaymentsAPI ERROR: " . $response['message']);
            return $response; // Возвращаем ошибку с деталями
        }

        error_log("NOWPaymentsAPI ERROR: Не удалось создать выплату");
        return false;
    }

    /**
     * Выполнить HTTP запрос к API
     */
    private function makeRequest($method, $url, $data = null, $authType = 'bearer', $usePublicKey = false) {
        // Выбираем правильный ключ
        $keyToUse = $usePublicKey ? $this->publicKey : $this->apiKey;

        if ($authType === 'x-api-key') {
            $headers = [
                'x-api-key: ' . $keyToUse,
                'Content-Type: application/json'
            ];
        } elseif ($authType === 'both') {
            // Для payout API нужны оба заголовка с реальным JWT токеном
            $jwtToken = $this->getJWTToken();
            if ($jwtToken) {
                $headers = [
                    'x-api-key: ' . $keyToUse,
                    'Authorization: Bearer ' . $jwtToken,
                    'Content-Type: application/json'
                ];
            } else {
                // Если не удалось получить JWT, используем API ключ как токен
                $headers = [
                    'x-api-key: ' . $keyToUse,
                    'Authorization: Bearer ' . $keyToUse,
                    'Content-Type: application/json'
                ];
            }
        } else {
            // Для bearer пробуем сначала JWT токен, потом API ключ
            $jwtToken = $this->getJWTToken();
            $tokenToUse = $jwtToken ?: $keyToUse;
            $headers = [
                'Authorization: Bearer ' . $tokenToUse,
                'Content-Type: application/json'
            ];
        }

        error_log("NOWPaymentsAPI INFO: Отправка запроса {$method} {$url} с заголовками: " . json_encode($headers));

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // Отключаем проверку SSL для разработки
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

        if ($method === 'POST') {
            curl_setopt($ch, CURLOPT_POST, true);
            if ($data) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            }
        }

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            error_log("NOWPaymentsAPI CURL ERROR: " . $error);
            return false;
        }

        if ($httpCode !== 200) {
            error_log("NOWPaymentsAPI HTTP INFO: Code {$httpCode}, Response: {$response}");

            // Парсим ошибку для детального сообщения
            $errorData = json_decode($response, true);
            if ($errorData) {
                return [
                    'error' => true,
                    'http_code' => $httpCode,
                    'message' => $errorData['message'] ?? 'Unknown error',
                    'code' => $errorData['code'] ?? 'UNKNOWN',
                    'details' => $errorData
                ];
            }

            return false;
        }

        $decodedResponse = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log("NOWPaymentsAPI JSON ERROR: " . json_last_error_msg());
            return false;
        }

        return $decodedResponse;
    }

    /**
     * Проверить статус выплаты с автоматическим переключением методов авторизации
     */
    public function getPayoutStatus($payoutId) {
        $url = $this->apiUrl . '/payout/' . $payoutId;

        $authMethods = ['both', 'bearer', 'x-api-key'];

        foreach ($authMethods as $authMethod) {
            $response = $this->makeRequest('GET', $url, null, $authMethod);
            if ($response !== false) {
                return $response;
            }
        }

        return false;
    }

    /**
     * Получить список доступных валют
     */
    public function getAvailableCurrencies() {
        $url = $this->apiUrl . '/currencies';
        return $this->makeRequest('GET', $url, null, 'x-api-key');
    }

    /**
     * Проверить баланс аккаунта с автоматическим переключением методов авторизации
     */
    public function getAccountBalance() {
        $url = $this->apiUrl . '/balance';

        $authMethods = ['x-api-key', 'both', 'bearer'];

        foreach ($authMethods as $authMethod) {
            $response = $this->makeRequest('GET', $url, null, $authMethod);
            if ($response !== false) {
                return $response;
            }
        }

        return false;
    }

    /**
     * Найти валюту с достаточным балансом для тестовой выплаты
     */
    public function findCurrencyWithBalance($minAmount = 0.00001) {
        $balance = $this->getAccountBalance();

        if (!$balance) {
            return false;
        }

        error_log("NOWPaymentsAPI INFO: Проверка баланса для поиска валюты с достаточными средствами");
        error_log("NOWPaymentsAPI INFO: Минимальная сумма: " . $minAmount);

        foreach ($balance as $currency => $data) {
            if (isset($data['amount']) && $data['amount'] > $minAmount) {
                error_log("NOWPaymentsAPI INFO: Найдена валюта {$currency} с балансом {$data['amount']}");
                return [
                    'currency' => $currency,
                    'balance' => $data['amount'],
                    'available' => $data['amount'] - ($data['pendingAmount'] ?? 0)
                ];
            }
        }

        error_log("NOWPaymentsAPI WARNING: Не найдено валют с достаточным балансом");
        return false;
    }

    /**
     * Автоматическая конвертация из доступной валюты в запрашиваемую
     */
    public function createPayoutWithAutoConversion($targetAddress, $targetCurrency, $targetAmount) {
        error_log("NOWPaymentsAPI INFO: Попытка создания выплаты с автоконвертацией");
        error_log("NOWPaymentsAPI INFO: Цель - {$targetAmount} {$targetCurrency} на адрес {$targetAddress}");

        // Сначала пробуем создать выплату напрямую
        $directResult = $this->createSinglePayout($targetAddress, $targetCurrency, $targetAmount);

        // Если выплата создана успешно, возвращаем результат
        if ($directResult && !isset($directResult['error'])) {
            error_log("NOWPaymentsAPI SUCCESS: Прямая выплата создана успешно");
            return $directResult;
        }

        // Если ошибка не связана с недостаточным балансом, возвращаем ошибку
        if (!isset($directResult['error']) || $directResult['code'] !== 'BAD_CREATE_WITHDRAWAL_REQUEST') {
            error_log("NOWPaymentsAPI ERROR: Ошибка не связана с балансом, автоконвертация невозможна");
            return $directResult;
        }

        error_log("NOWPaymentsAPI INFO: Прямая выплата не удалась, пробуем автоконвертацию");

        // Получаем баланс всех валют
        $balance = $this->getAccountBalance();
        if (!$balance) {
            error_log("NOWPaymentsAPI ERROR: Не удалось получить баланс для автоконвертации");
            return ['error' => true, 'message' => 'Не удалось получить баланс аккаунта'];
        }

        // Ищем валюты с достаточным балансом для конвертации
        $conversionOptions = [];

        foreach ($balance as $sourceCurrency => $data) {
            $availableAmount = $data['amount'] - ($data['pendingAmount'] ?? 0);

            if ($availableAmount <= 0) {
                continue; // Пропускаем валюты без баланса
            }

            // Получаем курс конвертации из исходной валюты в целевую
            $estimate = $this->getEstimateAmount($availableAmount, $sourceCurrency, $targetCurrency);

            if ($estimate && isset($estimate['estimated_amount'])) {
                $convertedAmount = $estimate['estimated_amount'];

                if ($convertedAmount >= $targetAmount) {
                    // Рассчитываем, сколько исходной валюты нужно для получения целевой суммы
                    $neededSourceAmount = ($targetAmount / $convertedAmount) * $availableAmount;

                    $conversionOptions[] = [
                        'source_currency' => $sourceCurrency,
                        'source_amount' => $neededSourceAmount,
                        'available_amount' => $availableAmount,
                        'target_amount' => $targetAmount,
                        'conversion_rate' => $convertedAmount / $availableAmount
                    ];

                    error_log("NOWPaymentsAPI INFO: Найдена опция конвертации: {$neededSourceAmount} {$sourceCurrency} -> {$targetAmount} {$targetCurrency}");
                }
            }
        }

        if (empty($conversionOptions)) {
            error_log("NOWPaymentsAPI ERROR: Не найдено валют для автоконвертации");
            return [
                'error' => true,
                'message' => "Недостаточно средств для конвертации в {$targetCurrency}. Пополните баланс в панели NOWPayments",
                'code' => 'INSUFFICIENT_BALANCE_FOR_CONVERSION'
            ];
        }

        // Выбираем лучшую опцию (с наименьшим расходом исходной валюты)
        usort($conversionOptions, function($a, $b) {
            return $a['source_amount'] <=> $b['source_amount'];
        });

        $bestOption = $conversionOptions[0];
        $sourceCurrency = $bestOption['source_currency'];
        $sourceAmount = $bestOption['source_amount'];

        error_log("NOWPaymentsAPI INFO: Выбрана лучшая опция конвертации: {$sourceAmount} {$sourceCurrency}");

        // Получаем правильный адрес для исходной валюты
        $sourceAddress = $this->getCompatibleAddress($targetAddress, $sourceCurrency, $targetCurrency);

        // Создаем выплату в исходной валюте
        $conversionResult = $this->createSinglePayout($sourceAddress, $sourceCurrency, $sourceAmount);

        if ($conversionResult && !isset($conversionResult['error'])) {
            error_log("NOWPaymentsAPI SUCCESS: Выплата с автоконвертацией создана успешно");

            // Добавляем информацию о конвертации в результат
            $conversionResult['auto_conversion'] = [
                'original_request' => [
                    'currency' => $targetCurrency,
                    'amount' => $targetAmount,
                    'address' => $targetAddress
                ],
                'actual_payout' => [
                    'currency' => $sourceCurrency,
                    'amount' => $sourceAmount,
                    'address' => $sourceAddress
                ],
                'conversion_info' => "Автоматически конвертировано из {$sourceCurrency} в {$targetCurrency}",
                'address_note' => $sourceAddress !== $targetAddress ? "Использован безопасный тестовый адрес для {$sourceCurrency}" : "Адрес совместим с обеими валютами"
            ];

            return $conversionResult;
        }

        error_log("NOWPaymentsAPI ERROR: Автоконвертация также не удалась");
        return $conversionResult ?: ['error' => true, 'message' => 'Автоконвертация не удалась'];
    }

    /**
     * Получить совместимый адрес для валюты при автоконвертации
     */
    public function getCompatibleAddress($originalAddress, $sourceCurrency, $targetCurrency) {
        // Если валюты одинаковые, возвращаем оригинальный адрес
        if ($sourceCurrency === $targetCurrency) {
            return $originalAddress;
        }

        // Безопасные тестовые адреса для разных валют
        $testAddresses = [
            'btc' => '**********************************', // Genesis block address
            'eth' => '******************************************', // Null address
            'ltc' => 'LTC1QW508D6QEJXTDG4Y5R3ZARVARY0C5XW7KV8F3T4', // Bech32 example
            'trx' => 'TLsV52sRDL79HXGGm9yzwKiW6SLtQ4KyUh', // TRON example
            'usdttrc20' => 'TLsV52sRDL79HXGGm9yzwKiW6SLtQ4KyUh', // TRON USDT
            'usdterc20' => '******************************************', // ETH USDT
            'bnb' => 'bnb1grpf0955h0ykzq3ar5nmum7y6gdfl6lxfn46h2', // BNB example
            'doge' => 'DH5yaieqoZN36fDVciNyRueRGvGLR3mr7L', // DOGE example
        ];

        // Проверяем, есть ли тестовый адрес для исходной валюты
        if (isset($testAddresses[$sourceCurrency])) {
            $testAddress = $testAddresses[$sourceCurrency];
            error_log("NOWPaymentsAPI INFO: Автоконвертация {$targetCurrency} -> {$sourceCurrency}: используем тестовый адрес {$testAddress}");
            return $testAddress;
        }

        // Если нет тестового адреса, пробуем использовать оригинальный
        error_log("NOWPaymentsAPI WARNING: Нет тестового адреса для {$sourceCurrency}, используем оригинальный");
        return $originalAddress;
    }

    /**
     * Проверить подпись IPN уведомления
     */
    public function verifyIPNSignature($data, $signature) {
        $expectedSignature = hash_hmac('sha512', $data, $this->ipnSecret);
        return hash_equals($expectedSignature, $signature);
    }

    /**
     * Выполнить HTTP запрос без авторизации (для получения JWT токена)
     */
    private function makeRequestWithoutAuth($method, $url, $data = null) {
        $headers = [
            'Content-Type: application/json'
        ];

        error_log("NOWPaymentsAPI INFO: Отправка запроса {$method} {$url} без авторизации");
        if ($data) {
            error_log("NOWPaymentsAPI INFO: Данные запроса: " . json_encode($data));
        }

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

        if ($method === 'POST') {
            curl_setopt($ch, CURLOPT_POST, true);
            if ($data) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            }
        }

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch)) {
            $error = curl_error($ch);
            curl_close($ch);
            error_log("NOWPaymentsAPI CURL ERROR: {$error}");
            return false;
        }

        curl_close($ch);

        if ($httpCode !== 200) {
            error_log("NOWPaymentsAPI HTTP INFO: Code {$httpCode}, Response: {$response}");
            return false;
        }

        error_log("NOWPaymentsAPI SUCCESS: Ответ получен: " . $response);
        return json_decode($response, true);
    }
}
?>
