Stack trace:
Frame         Function      Args
0007FFFF9B70  00021006118E (00021028DEE8, 000210272B3E, 0007FFFF9B70, 0007FFFF8A70) msys-2.0.dll+0x2118E
0007FFFF9B70  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFF9B70  0002100469F2 (00021028DF99, 0007FFFF9A28, 0007FFFF9B70, 000000000000) msys-2.0.dll+0x69F2
0007FFFF9B70  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF9B70  00021006A545 (0007FFFF9B80, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFF9B80, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFCBA590000 ntdll.dll
7FFCBA380000 KERNEL32.DLL
7FFCB7E70000 KERNELBASE.dll
7FFCB86B0000 USER32.dll
7FFCB84B0000 win32u.dll
7FFCB8920000 GDI32.dll
7FFCB7D60000 gdi32full.dll
7FFCB84E0000 msvcp_win.dll
7FFCB8190000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFCB9D10000 advapi32.dll
7FFCB9F80000 msvcrt.dll
7FFCB8B00000 sechost.dll
7FFCB89D0000 RPCRT4.dll
7FFCB7610000 CRYPTBASE.DLL
7FFCB8420000 bcryptPrimitives.dll
7FFCB9070000 IMM32.DLL
