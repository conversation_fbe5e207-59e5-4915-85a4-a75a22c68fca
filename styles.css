/* ======================================== */
/* styles.css - Основной файл стилей */
/* ======================================== */

/* --- Базовые настройки и Цветовая Палитра --- */
:root {
  --app-bg-color: #1c1c1e;
  --app-secondary-bg-color: #2c2c2e;
  --app-text-color: #ffffff;
  --app-hint-color: #8e8e93;
  --app-primary-color: #8b5cf6;
  --app-primary-text-color: #ffffff;
  --app-secondary-button-bg: #ff6b35;
  --app-secondary-button-text: #ffffff;
  --app-destructive-color: #ef4444;
  --app-separator-color: #38383a;

  /* Новые цвета для кнопок */
  --purple-color: #8b5cf6;
  --orange-color: #ff6b35;
  --blue-color: #3b82f6;
  --red-color: #ef4444;
  --yellow-color: #eab308;
  --black-color: #000000;
  --page-transition-duration: 0.3s;
}

/* --- Глобальные стили и Box Sizing --- */
*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  height: 100%;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  color: var(--app-text-color);
  background-color: #0fc724;
  background-image: url("images/background-money.png");
  background-repeat: no-repeat;
  background-position: center bottom;
  background-size: contain;
  background-attachment: fixed;
  overscroll-behavior: none;
  display: flex;
  flex-direction: column;
  min-height: 100%;
}

body::before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(28, 28, 30, 0.85);
  z-index: -1;
}

/* --- Основной контейнер --- */
.app-container {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: 0 15px 75px 15px;
  position: relative;
  overflow-x: hidden;
  z-index: 1;
}

/* --- Шапка (Header) --- */
.app-header {
  width: 100%;
  background-color: rgb(2 30 4 / 80%);
  padding: 10px 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--app-separator-color);
  border-radius: 0px 0px 12px 12px;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 10;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  height: 55px;
}

.user-info {
  display: flex;
  align-items: center;
  min-width: 0;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  flex-shrink: 0;
  overflow: hidden;
}

.user-avatar .icon {
  margin: auto;
  filter: brightness(0) invert(1);
  width: 24px;
  height: 24px;
}

.user-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--app-text-color);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: calc(100vw - 200px);
}

.balance-info {
  display: flex;
  align-items: center;
  background-color: var(--app-secondary-button-bg);
  padding: 5px 12px;
  border-radius: 15px;
  flex-shrink: 0;
}

.balance-amount {
  font-size: 16px;
  font-weight: bold;
  margin-right: 4px;
  color: var(--app-text-color);
}

.balance-currency {
  font-size: 14px;
  color: var(--app-hint-color);
}

.clickable-balance {
  position: absolute;
  right: 9px;
  top: 12px;
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.clickable-balance:hover {
  opacity: 0.8;
}

/* --- Секции для АНИМАЦИИ --- */
.app-main,
.app-section {
  display: flex;
  flex-direction: column;
  gap: 15px;
  width: 100%;
  position: absolute;
  top: 63px;
  left: 0;
  padding: 15px;
  padding-bottom: 86px;
  min-height: calc(100vh - 55px - 75px);
  opacity: 1;
  transform: translateX(0);
  transition: opacity var(--page-transition-duration) ease-in-out,
    transform var(--page-transition-duration) ease-in-out;
  will-change: opacity, transform;
  background-color: transparent;
  z-index: 1;
}

.app-main.active-section,
.app-section.active-section {
  z-index: 2;
}

.page-hidden {
  display: none !important;
  opacity: 0;
  pointer-events: none;
}

.page-enter {
  opacity: 0;
  transform: translateX(20px);
  z-index: 2;
}

.page-enter-active {
  opacity: 1;
  transform: translateX(0);
  z-index: 2;
}

.page-leave-active {
  opacity: 0;
  transform: translateX(-20px);
  z-index: 1;
  pointer-events: none;
}

.app-main h2,
.app-section h2 {
  text-align: center;
  margin-top: 0;
  margin-bottom: 10px;
  color: var(--app-text-color);
  font-weight: 600;
  font-size: 20px;
}

/* --- Статус бар --- */
.status-message {
  padding: 10px;
  font-size: 13px;
  text-align: center;
  min-height: 20px;
  border-radius: 8px;
  word-wrap: break-word;
  margin-bottom: 15px;
  background-color: var(--app-secondary-bg-color);
  color: var(--app-hint-color);
  transition: all 0.3s;
  opacity: 1;
}

.status-message:empty {
  opacity: 0;
  padding: 0;
  min-height: 0;
  margin-bottom: 0;
}

.status-message.success {
  color: var(--app-primary-text-color);
  font-weight: bold;
  background-color: var(--app-primary-color);
}

.status-message.error {
  color: #ffffff;
  font-weight: bold;
  background-color: var(--app-destructive-color);
}

/* --- Кнопки Действий --- */
.action-button {
  width: 100%;
  padding: 14px 20px;
  font-size: 16px;
  font-weight: 600;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  transition: background-color 0.2s ease, transform 0.1s ease, box-shadow 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  appearance: none;
  -webkit-appearance: none;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-button.primary-action {
  background-color: var(--app-primary-color);
  color: var(--app-primary-text-color);
}

.action-button.primary-action:hover:not(:disabled) {
  background-color: color-mix(in srgb, var(--app-primary-color) 90%, black 10%);
}

.action-button.secondary-action {
  background-color: var(--app-secondary-button-bg);
  color: var(--app-secondary-button-text);
}

.action-button.secondary-action:hover:not(:disabled) {
  background-color: color-mix(in srgb, var(--app-secondary-button-bg) 90%, black 10%);
}

.action-button:active:not(:disabled) {
  transform: scale(0.98);
  box-shadow: none;
}

.action-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: #555 !important;
  color: var(--app-hint-color) !important;
  box-shadow: none;
  transform: none;
}

/* Эффект нажатой кнопки */
.action-button.pressed {
  transform: translateY(2px);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  opacity: 0.8;
}

/* Счетчик обратного отсчета */
.countdown-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: bold;
  color: white;
  border-radius: 12px;
  z-index: 10;
}

/* Цвета для разных типов кнопок */
.action-button.purple-button {
  background-color: var(--purple-color);
}

.action-button.orange-button {
  background-color: var(--orange-color);
}

.action-button.blue-button {
  background-color: var(--blue-color);
}

.action-button.red-button {
  background-color: var(--red-color);
}

.action-button.yellow-button {
  background-color: var(--yellow-color);
  color: var(--black-color);
}

/* === Секция Друзей === */
.friends-block {
  background-color: var(--app-secondary-bg-color);
  padding: 15px;
  border-radius: 12px;
  margin-bottom: 15px;
}

.friends-block h3 {
  margin-top: 0;
  margin-bottom: 8px;
  color: var(--app-text-color);
  font-size: 16px;
  font-weight: 600;
}

.friends-block p {
  font-size: 14px;
  color: var(--app-text-color);
  margin-top: 0;
  margin-bottom: 12px;
  line-height: 1.4;
}

.friends-block p.hint {
  font-size: 12px;
  font-style: italic;
  color: var(--app-hint-color);
}

.referral-link-area {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 10px;
}

.referral-link-area input[type="text"] {
  flex-grow: 1;
  padding: 10px 12px;
  border: 1px solid var(--app-separator-color);
  background-color: var(--app-bg-color);
  color: var(--app-text-color);
  border-radius: 10px;
  font-size: 14px;
}

.referral-link-area .copy-button {
  padding: 0;
  background-color: var(--app-secondary-button-bg);
  border: none;
  border-radius: 10px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  flex-shrink: 0;
}

.referral-link-area .copy-button:hover:not(:disabled) {
  background-color: color-mix(in srgb, var(--app-secondary-button-bg) 80%, white 20%);
}

.referral-link-area .copy-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: #555 !important;
}

/* === Навигация === */
.app-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: var(--app-secondary-bg-color);
  border-top: 1px solid var(--app-separator-color);
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 10px 0;
  z-index: 10;
  height: 75px;
}

.nav-button {
  background: none;
  border: none;
  color: var(--app-hint-color);
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  padding: 10px;
  transition: color 0.2s ease;
  flex: 1;
  max-width: 100px;
}

.nav-button.active {
  color: var(--app-primary-color);
}

.nav-button:hover {
  color: var(--app-text-color);
}

.nav-icon {
  width: 24px;
  height: 24px;
  fill: currentColor;
}

.nav-text {
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}
