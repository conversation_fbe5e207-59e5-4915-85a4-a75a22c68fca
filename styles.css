/* ======================================== */
/* styles.css - Ð¤Ð¸Ð½Ð°Ð»ÑŒÐ½Ð°Ñ v2 Ñ CSS ÑÐ¿Ñ€Ð°Ð¹Ñ‚Ð°Ð¼Ð¸, Ñ„Ð¾Ð½Ð¾Ð¼, Ð°Ð½Ð¸Ð¼Ð°Ñ†Ð¸ÑÐ¼Ð¸ Ð¸ Ð’ÐžÐ¡Ð¡Ð¢ÐÐÐžÐ’Ð›Ð•ÐÐÐ«ÐœÐ˜ ÑÑ‚Ð¸Ð»ÑÐ¼Ð¸ ÑÐµÐºÑ†Ð¸Ð¹ */
/* ======================================== */

/* --- Ð‘Ð°Ð·Ð¾Ð²Ñ‹Ðµ Ð½Ð°ÑÑ‚Ñ€Ð¾Ð¹ÐºÐ¸ Ð¸ ÐšÐ°ÑÑ‚Ð¾Ð¼Ð½Ð°Ñ Ð¦Ð²ÐµÑ‚Ð¾Ð²Ð°Ñ ÐŸÐ°Ð»Ð¸Ñ‚Ñ€Ð° --- */
:root {
  --app-bg-color: #1c1c1e;
  --app-secondary-bg-color: #2c2c2e;
  --app-text-color: #ffffff;
  --app-hint-color: #8e8e93;
  --app-primary-color: #8b5cf6;
  --app-primary-text-color: #ffffff;
  --app-secondary-button-bg: #ff6b35;
  --app-secondary-button-text: #ffffff;
  --app-destructive-color: #ef4444;
  --app-separator-color: #38383a;

  /* Новые цвета для кнопок */
  --purple-color: #8b5cf6;
  --orange-color: #ff6b35;
  --blue-color: #3b82f6;
  --red-color: #ef4444;
  --yellow-color: #eab308;
  --black-color: #000000;
  --page-transition-duration: 0.3s;
  /* --- ÐÐ°ÑÑ‚Ñ€Ð¾Ð¹ÐºÐ¸ ÑÐ¿Ñ€Ð°Ð¹Ñ‚Ð° --- */
  --sprite-url: "images/sprite.svg"; /* <-- ÐŸÐ£Ð¢Ð¬ Ðš Ð¢Ð’ÐžÐ•ÐœÐ£ PNG Ð¡ÐŸÐ ÐÐ™Ð¢Ð£ */
  --icon-width: 32px; /* <-- Ð¨Ð˜Ð Ð˜ÐÐ Ð¾Ð´Ð½Ð¾Ð¹ Ð¸ÐºÐ¾Ð½ÐºÐ¸ */
  --icon-height: 32px; /* <-- Ð’Ð«Ð¡ÐžÐ¢Ð Ð¾Ð´Ð½Ð¾Ð¹ Ð¸ÐºÐ¾Ð½ÐºÐ¸ */
  /* Ð Ð°ÑÑÑ‡Ð¸Ñ‚Ñ‹Ð²Ð°ÐµÐ¼ Ð¾Ð±Ñ‰Ð¸Ð¹ Ñ€Ð°Ð·Ð¼ÐµÑ€ ÑÐ¿Ñ€Ð°Ð¹Ñ‚Ð° (Ð¿Ñ€ÐµÐ´Ð¿Ð¾Ð»Ð°Ð³Ð°ÐµÐ¼ 3x3) */
  --sprite-total-width: calc(var(--icon-width) * 3);
  --sprite-total-height: calc(var(--icon-height) * 3);
}

/* --- Ð“Ð»Ð¾Ð±Ð°Ð»ÑŒÐ½Ñ‹Ðµ ÑÑ‚Ð¸Ð»Ð¸ Ð¸ Box Sizing --- */
*,
*::before,
*::after {
  box-sizing: border-box;
}
html {
  scroll-behavior: smooth;
  height: 100%;
}
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica,
    Arial, sans-serif;
  color: var(--app-text-color);
  background-color: #0fc724;
  background-image: url("images/background-money.png"); /* <-- ÐŸÐ£Ð¢Ð¬ Ðš Ð¤ÐžÐÐ£ */
  background-repeat: no-repeat;
  background-position: center bottom;
  background-size: contain;
  background-attachment: fixed;
  overscroll-behavior: none;
  display: flex;
  flex-direction: column;
  min-height: 100%;
}
body::before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(28, 28, 30, 0.85);
  z-index: -1;
}

/* --- ÐžÑÐ½Ð¾Ð²Ð½Ð¾Ð¹ ÐºÐ¾Ð½Ñ‚ÐµÐ¹Ð½ÐµÑ€ --- */
.app-container {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: 0 15px 75px 15px;
  position: relative;
  overflow-x: hidden;
  z-index: 1;
}

/* --- Ð¨Ð°Ð¿ÐºÐ° (Header) --- */
.app-header {
  width: 100%;
  background-color: rgb(2 30 4 / 80%);
  padding: 10px 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--app-separator-color);
  border-radius: 0px 0px 12px 12px;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 10;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  height: 55px;
}
.user-info {
  display: flex;
  align-items: center;
  min-width: 0;
}
.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  flex-shrink: 0;
  overflow: hidden;
}
.user-avatar .icon {
  margin: auto;
  filter: brightness(0) invert(1);
  width: 24px;
  height: 24px;
  background-size: calc(var(--sprite-total-width) / (var(--icon-width) / 24px))
    calc(var(--sprite-total-height) / (var(--icon-height) / 24px)); /* ÐœÐ°ÑÑˆÑ‚Ð°Ð±Ð¸Ñ€ÑƒÐµÐ¼ ÑÐ¿Ñ€Ð°Ð¹Ñ‚ */
}
.user-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--app-text-color);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: calc(100vw - 200px);
}
.balance-info {
  display: flex;
  align-items: center;
  background-color: var(--app-secondary-button-bg);
  padding: 5px 12px;
  border-radius: 15px;
  flex-shrink: 0;
}
.balance-icon {
  margin-right: 6px;
  filter: brightness(0) saturate(100%) invert(69%) sepia(68%) saturate(497%)
    hue-rotate(83deg) brightness(99%) contrast(93%);
  width: 18px;
  height: 18px;
  background-image: url("./images/sprite.svg");
  background-size: 88px;
  background-position: 256px 258px;
}
.balance-amount {
  font-size: 16px;
  font-weight: bold;
  margin-right: 4px;
  color: var(--app-text-color);
}
.balance-currency {
  font-size: 14px;
  color: var(--app-hint-color);
}
.clickable-balance {
  position: absolute;
  right: 9px;
  top: 12px;
  cursor: pointer;
  transition: opacity 0.2s ease;
}
.clickable-balance:hover {
  opacity: 0.8;
}

/* --- ÐžÐ±Ñ‰Ð¸Ðµ ÑÑ‚Ð¸Ð»Ð¸ Ð´Ð»Ñ Ð˜ÐšÐžÐÐžÐš Ð¸Ð· ÑÐ¿Ñ€Ð°Ð¹Ñ‚Ð° --- */
.icon {
  display: inline-block;
  width: var(--icon-width);
  height: var(--icon-height);
  /* background-image: url(var(--sprite-url)); */
  background-repeat: no-repeat;
  vertical-align: middle;
  background-size: var(--sprite-total-width) var(--sprite-total-height); /* Ð£ÐºÐ°Ð·Ñ‹Ð²Ð°ÐµÐ¼ Ð¾Ð±Ñ‰Ð¸Ð¹ Ñ€Ð°Ð·Ð¼ÐµÑ€ ÑÐ¿Ñ€Ð°Ð¹Ñ‚Ð° */
}
/* ÐŸÐ¾Ð·Ð¸Ñ†Ð¸Ð¸ Ð¸ÐºÐ¾Ð½Ð¾Ðº */
.icon-energy {
  background-position: 0 0;
}
.icon-money {
  background-position: calc(var(--icon-width) * -1) 0;
}
.icon-ruble {
  background-position: calc(var(--icon-width) * -2) 0;
}
.icon-link {
  background-position: 0 calc(var(--icon-height) * -1);
}
.icon-play {
  background-position: calc(var(--icon-width) * -1)
    calc(var(--icon-height) * -1);
}
.icon-video-camera {
  background-position: calc(var(--icon-width) * -2)
    calc(var(--icon-height) * -1);
}
.icon-home {
  background-position: 0 calc(var(--icon-height) * -2);
}
.icon-dollar {
  background-position: calc(var(--icon-width) * -1)
    calc(var(--icon-height) * -2);
}
.icon-friends {
  background-position: calc(var(--icon-width) * -2)
    calc(var(--icon-height) * -2);
}

/* --- Ð¡ÐµÐºÑ†Ð¸Ð¸ Ð´Ð»Ñ ÐÐÐ˜ÐœÐÐ¦Ð˜Ð˜ --- */
.app-main,
.app-section {
  display: flex;
  flex-direction: column;
  gap: 15px;
  width: 100%;
  position: absolute;
  top: 63px;
  left: 0;
  padding: 15px;
  padding-bottom: 86px;
  min-height: calc(100vh - 55px - 75px);
  opacity: 1;
  transform: translateX(0);
  transition: opacity var(--page-transition-duration) ease-in-out,
    transform var(--page-transition-duration) ease-in-out;
  will-change: opacity, transform;
  background-color: transparent;
  z-index: 1;
}
.app-main.active-section,
.app-section.active-section {
  z-index: 2;
}
.page-hidden {
  display: none !important;
  opacity: 0;
  pointer-events: none;
}
.page-enter {
  opacity: 0;
  transform: translateX(20px);
  z-index: 2;
}
.page-enter-active {
  opacity: 1;
  transform: translateX(0);
  z-index: 2;
}
.page-leave-active {
  opacity: 0;
  transform: translateX(-20px);
  z-index: 1;
  pointer-events: none;
}
.app-main h2,
.app-section h2 {
  text-align: center;
  margin-top: 0;
  margin-bottom: 10px;
  color: var(--app-text-color);
  font-weight: 600;
  font-size: 20px;
}

/* --- Ð¡Ñ‚Ð°Ñ‚ÑƒÑ Ð±Ð°Ñ€ --- */
.status-message {
  padding: 10px;
  font-size: 13px;
  text-align: center;
  min-height: 20px;
  border-radius: 8px;
  word-wrap: break-word;
  margin-bottom: 15px;
  background-color: var(--app-secondary-bg-color);
  color: var(--app-hint-color);
  transition: all 0.3s;
  opacity: 1;
}
.status-message:empty {
  opacity: 0;
  padding: 0;
  min-height: 0;
  margin-bottom: 0;
}
.status-message.success {
  color: var(--app-primary-text-color);
  font-weight: bold;
  background-color: var(--app-primary-color);
}
.status-message.error {
  color: #ffffff;
  font-weight: bold;
  background-color: var(--app-destructive-color);
}

/* --- ÐšÐ½Ð¾Ð¿ÐºÐ¸ Ð”ÐµÐ¹ÑÑ‚Ð²Ð¸Ð¹ Ñ Ð˜ÐºÐ¾Ð½ÐºÐ°Ð¼Ð¸ --- */
.action-button {
  width: 100%;
  padding: 14px 20px;
  font-size: 16px;
  font-weight: 600;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  transition: background-color 0.2s ease, transform 0.1s ease,
    box-shadow 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  appearance: none;
  -webkit-appearance: none;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.action-button .button-icon {
  position: absolute;
  left: 29px;
  width: 30px;
  height: 30px;
  /* Ð‘ÐµÐ»Ñ‹Ðµ Ð¿Ð¾ ÑƒÐ¼Ð¾Ð»Ñ‡Ð°Ð½Ð¸ÑŽ */
  /* ÐœÐ°ÑÑˆÑ‚Ð°Ð± ÑÐ¿Ñ€Ð°Ð¹Ñ‚Ð° */
  background-image: url(./images/sprite.svg);
  background-size: 88px;
  background-position: 260px 236px;
}
.action-button.primary-action {
  background-color: var(--app-primary-color);
  color: var(--app-primary-text-color);
}
.action-button.primary-action .button-icon {
  /* Ð£Ð¶Ðµ Ð±ÐµÐ»Ð°Ñ */
}
.action-button.primary-action:hover:not(:disabled) {
  background-color: color-mix(in srgb, var(--app-primary-color) 90%, black 10%);
}
.action-button.secondary-action {
  background-color: var(--app-secondary-button-bg);
  color: var(--app-secondary-button-text);
}
.action-button.secondary-action .button-icon {
  /* Ð£Ð¶Ðµ Ð±ÐµÐ»Ð°Ñ */
}
.action-button.secondary-action:hover:not(:disabled) {
  background-color: color-mix(
    in srgb,
    var(--app-secondary-button-bg) 90%,
    black 10%
  );
}
.action-button:active:not(:disabled) {
  transform: scale(0.98);
  box-shadow: none;
}
.action-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: #555 !important;
  color: var(--app-hint-color) !important;
  box-shadow: none;
  transform: none;
}
.action-button:disabled .button-icon {
  filter: brightness(0) invert(0.6);
}

/* Эффект нажатой кнопки */
.action-button.pressed {
  transform: translateY(2px);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  opacity: 0.8;
}

/* Счетчик обратного отсчета */
.countdown-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: bold;
  color: white;
  border-radius: 12px;
  z-index: 10;
}

/* Цвета для разных типов кнопок */
.action-button.purple-button {
  background-color: var(--purple-color);
}

.action-button.orange-button {
  background-color: var(--orange-color);
}

.action-button.blue-button {
  background-color: var(--blue-color);
}

.action-button.red-button {
  background-color: var(--red-color);
}

.action-button.yellow-button {
  background-color: var(--yellow-color);
  color: var(--black-color);
}

/* === Ð¡ÐµÐºÑ†Ð¸Ñ Ð”Ñ€ÑƒÐ·ÑŒÑ (Ð’ÐžÐ¡Ð¡Ð¢ÐÐÐžÐ’Ð›Ð•ÐÐÐ«Ð•/ÐŸÐ ÐžÐ’Ð•Ð Ð•ÐÐÐ«Ð• Ð¡Ð¢Ð˜Ð›Ð˜) === */
.friends-block {
  background-color: var(--app-secondary-bg-color);
  padding: 15px;
  border-radius: 12px;
  margin-bottom: 15px;
}
.friends-block h3 {
  margin-top: 0;
  margin-bottom: 8px;
  color: var(--app-text-color);
  font-size: 16px;
  font-weight: 600;
}
.friends-block p {
  font-size: 14px;
  color: var(--app-text-color); /* Ð¢ÐµÐºÑÑ‚ Ñ‚Ð¾Ð¶Ðµ Ð±ÐµÐ»Ñ‹Ð¹ */
  margin-top: 0;
  margin-bottom: 12px;
  line-height: 1.4;
}
.friends-block p.hint {
  /* ÐŸÐ¾Ð´ÑÐºÐ°Ð·ÐºÐ¸ ÑÐ´ÐµÐ»Ð°ÐµÐ¼ Ñ‚ÑƒÑÐºÐ»ÐµÐµ */
  font-size: 12px;
  font-style: italic;
  color: var(--app-hint-color);
}
.referral-link-area {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 10px;
}
.referral-link-area input[type="text"] {
  flex-grow: 1;
  padding: 10px 12px; /* Ð£Ð²ÐµÐ»Ð¸Ñ‡Ð¸Ð¼ Ð½ÐµÐ¼Ð½Ð¾Ð³Ð¾ */
  border: 1px solid var(--app-separator-color);
  background-color: var(--app-bg-color); /* Ð¢ÐµÐ¼Ð½Ñ‹Ð¹ Ñ„Ð¾Ð½ Ð¿Ð¾Ð»Ñ */
  color: var(--app-text-color);
  border-radius: 10px; /* Ð¡ÐºÑ€ÑƒÐ³Ð»Ð¸Ð¼ */
  font-size: 14px;
}
.referral-link-area .copy-button {
  padding: 0; /* Ð£Ð±Ð¸Ñ€Ð°ÐµÐ¼ Ð²Ð½ÑƒÑ‚Ñ€ÐµÐ½Ð½Ð¸Ð¹ padding */
  background-color: var(--app-secondary-button-bg);
  border: none;
  border-radius: 10px; /* Ð¡ÐºÑ€ÑƒÐ³Ð»Ð¸Ð¼ */
  cursor: pointer;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px; /* ÐšÐ²Ð°Ð´Ñ€Ð°Ñ‚Ð½Ð°Ñ ÐºÐ½Ð¾Ð¿ÐºÐ° */
  height: 40px;
  flex-shrink: 0; /* ÐÐµ ÑÐ¶Ð¸Ð¼Ð°Ñ‚ÑŒ ÐºÐ½Ð¾Ð¿ÐºÑƒ */
}
.referral-link-area .copy-button .icon {
  width: 18px;
  height: 18px;
  filter: brightness(0) invert(1); /* Ð‘ÐµÐ»Ð°Ñ Ð¸ÐºÐ¾Ð½ÐºÐ° */
  background-size: calc(var(--sprite-total-width) / (var(--icon-width) / 18px))
    calc(var(--sprite-total-height) / (var(--icon-height) / 18px));
}
.referral-link-area .copy-button:hover:not(:disabled) {
  background-color: color-mix(
    in srgb,
    var(--app-secondary-button-bg) 80%,
    white 20%
  );
}
.referral-link-area .copy-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: #555 !important;
}
.placeholder-list {
  background-color: var(--app-bg-color);
  padding: 15px;
  border-radius: 8px;
  text-align: center;
  color: var(--app-hint-color);
  font-style: italic;
  border: 1px dashed var(--app-separator-color);
  margin-top: 10px;
}

/* Стили для истории выплат */
.withdrawal-history {
  max-height: 400px;
  overflow-y: auto;
}

.withdrawal-item {
  background-color: var(--app-bg-color);
  border: 1px solid var(--app-separator-color);
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 12px;
  position: relative;
}

.withdrawal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.withdrawal-amount {
  font-size: 18px;
  font-weight: bold;
  color: var(--app-text-color);
}

.withdrawal-status {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  text-transform: uppercase;
}

.status-pending {
  background-color: #ffc107;
  color: #000;
}

.status-processing {
  background-color: #17a2b8;
  color: #fff;
}

.status-completed {
  background-color: #28a745;
  color: #fff;
}

.status-failed {
  background-color: #dc3545;
  color: #fff;
}

.withdrawal-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  color: var(--app-hint-color);
}

.withdrawal-currency {
  font-weight: bold;
  color: var(--app-primary-color);
}

.withdrawal-address {
  font-size: 12px;
  color: var(--app-hint-color);
  word-break: break-all;
  background-color: var(--app-secondary-bg-color);
  padding: 6px 8px;
  border-radius: 6px;
  border: 1px solid var(--app-separator-color);
  margin-top: 8px;
}

.withdrawal-actions {
  display: flex;
  gap: 8px;
  margin-top: 10px;
}

.cancel-button {
  background-color: var(--app-destructive-color);
  color: #ffffff;
  border: none;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.cancel-button:hover {
  background-color: #e02d20;
}

.cancel-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Стили для статистики рефералов */
.referral-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 15px;
}

.stat-item {
  flex: 1;
  min-width: 120px;
  background-color: var(--app-bg-color);
  border-radius: 8px;
  padding: 10px;
  text-align: center;
}

.stat-label {
  font-size: 12px;
  color: var(--app-hint-color);
  margin-bottom: 5px;
}

.stat-value {
  font-size: 18px;
  font-weight: bold;
  color: var(--app-text-color);
}

.referrals-list {
  margin: 15px 0;
  max-height: 200px;
  overflow-y: auto;
  background-color: var(--app-bg-color);
  border-radius: 8px;
  padding: 10px;
}

.referral-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid var(--app-separator-color);
}

.referral-item:last-child {
  border-bottom: none;
}

/* Стили для формы вывода средств */
.withdrawal-form {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 15px;
}

.withdrawal-form label {
  font-size: 14px;
  color: var(--app-text-color);
  margin-bottom: -5px;
}

.withdrawal-form input[type="number"],
.withdrawal-form input[type="text"],
.withdrawal-form select {
  padding: 12px;
  border: 1px solid var(--app-separator-color);
  background-color: var(--app-bg-color);
  color: var(--app-text-color);
  border-radius: 10px;
  font-size: 14px;
  width: 100%;
}

.withdrawal-form select {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: url("data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%23FFFFFF%22%20d%3D%22M287%2069.4a17.6%2017.6%200%200%200-13-5.4H18.4c-5%200-9.3%201.8-12.9%205.4A17.6%2017.6%200%200%200%200%2082.2c0%205%201.8%209.3%205.4%2012.9l128%20127.9c3.6%203.6%207.8%205.4%2012.8%205.4s9.2-1.8%2012.8-5.4L287%2095c3.5-3.5%205.4-7.8%205.4-12.8%200-5-1.9-9.2-5.5-12.8z%22%2F%3E%3C%2Fsvg%3E");
  background-repeat: no-repeat;
  background-position: right 12px top 50%;
  background-size: 12px auto;
  padding-right: 30px;
}

.withdrawal-form input:focus,
.withdrawal-form select:focus {
  outline: none;
  border-color: var(--app-primary-color);
}

.error-message {
  color: var(--app-destructive-color) !important;
  font-weight: bold !important;
  font-style: normal !important;
}

/* Стили для истории выплат */
.withdrawal-item {
  background-color: var(--app-secondary-bg-color);
  border: 1px solid var(--app-separator-color);
  border-radius: 10px;
  padding: 12px;
  margin-bottom: 10px;
}

.withdrawal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.withdrawal-amount {
  font-weight: bold;
  font-size: 16px;
  color: var(--app-text-color);
}

.withdrawal-status {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  text-transform: uppercase;
}

.status-pending {
  background-color: #ffc107;
  color: #000;
}

.status-completed {
  background-color: #28a745;
  color: #fff;
}

.status-failed {
  background-color: #dc3545;
  color: #fff;
}

.withdrawal-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  color: var(--app-hint-color);
}

.withdrawal-currency {
  font-weight: bold;
  color: var(--app-primary-color);
}

.withdrawal-address {
  font-size: 12px;
  color: var(--app-hint-color);
  word-break: break-all;
  background-color: var(--app-bg-color);
  padding: 6px 8px;
  border-radius: 6px;
  border: 1px solid var(--app-separator-color);
}

.withdrawal-form label {
  font-size: 14px;
  color: var(--app-text-color);
  margin-bottom: -5px;
}

.withdrawal-form input[type="number"],
.withdrawal-form input[type="text"],
.withdrawal-form select {
  padding: 12px;
  border: 1px solid var(--app-separator-color);
  background-color: var(--app-bg-color);
  color: var(--app-text-color);
  border-radius: 10px;
  font-size: 14px;
  width: 100%;
}

.withdrawal-form select {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: url("data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%23FFFFFF%22%20d%3D%22M287%2069.4a17.6%2017.6%200%200%200-13-5.4H18.4c-5%200-9.3%201.8-12.9%205.4A17.6%2017.6%200%200%200%200%2082.2c0%205%201.8%209.3%205.4%2012.9l128%20127.9c3.6%203.6%207.8%205.4%2012.8%205.4s9.2-1.8%2012.8-5.4L287%2095c3.5-3.5%205.4-7.8%205.4-12.8%200-5-1.9-9.2-5.5-12.8z%22%2F%3E%3C%2Fsvg%3E");
  background-repeat: no-repeat;
  background-position: right 12px top 50%;
  background-size: 12px auto;
  padding-right: 30px;
}

.withdrawal-form input:focus,
.withdrawal-form select:focus {
  outline: none;
  border-color: var(--app-primary-color);
}

.error-message {
  color: var(--app-destructive-color) !important;
  font-weight: bold !important;
  font-style: normal !important;
}

/* Стили для подсказки с иконкой вопроса */
.help-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background-color: var(--app-hint-color);
  color: white;
  border-radius: 50%;
  font-size: 12px;
  font-weight: bold;
  cursor: pointer;
  margin-left: 8px;
  transition: background-color 0.2s ease;
}

.help-icon:hover {
  background-color: var(--app-primary-color);
}

.help-tooltip {
  position: relative;
  display: inline-block;
}

.help-tooltip .tooltip-text {
  visibility: hidden;
  width: 280px;
  background-color: var(--app-secondary-bg-color);
  color: var(--app-text-color);
  text-align: left;
  border-radius: 8px;
  padding: 12px;
  position: absolute;
  z-index: 1000;
  bottom: 125%;
  left: 50%;
  margin-left: -140px;
  opacity: 0;
  transition: opacity 0.3s;
  border: 1px solid var(--app-separator-color);
  font-size: 14px;
  line-height: 1.4;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.help-tooltip .tooltip-text::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: var(--app-separator-color) transparent transparent transparent;
}

.help-tooltip:hover .tooltip-text {
  visibility: visible;
  opacity: 1;
}

/* Стили для поля суммы к получению */
.crypto-amount-field {
  background-color: var(--app-secondary-bg-color);
  border: 1px solid var(--app-separator-color);
  color: var(--app-hint-color);
  font-style: italic;
  cursor: not-allowed;
}

.referral-name {
  font-size: 14px;
  color: var(--app-text-color);
}

.referral-info {
  font-size: 12px;
  color: var(--app-hint-color);
}

/* === Ð¡ÐµÐºÑ†Ð¸Ñ Ð—Ð°Ñ€Ð°Ð±Ð¾Ñ‚Ð¾Ðº (Ð’ÐžÐ¡Ð¡Ð¢ÐÐÐžÐ’Ð›Ð•ÐÐÐ«Ð•/ÐŸÐ ÐžÐ’Ð•Ð Ð•ÐÐÐ«Ð• Ð¡Ð¢Ð˜Ð›Ð˜) === */
.earn-block {
  background-color: var(--app-secondary-bg-color);
  padding: 15px;
  border-radius: 12px;
  margin-bottom: 15px;
}
.earn-block h3 {
  margin-top: 0;
  margin-bottom: 8px;
  color: var(--app-text-color);
  font-size: 16px;
  font-weight: 600;
}
.earn-block .hint {
  font-size: 12px;
  font-style: italic;
  color: var(--app-hint-color);
  margin-top: 5px;
  margin-bottom: 10px;
}
.earn-block .error-message {
  color: var(--app-destructive-color);
  font-weight: bold;
  font-style: normal;
  display: block; /* Ð§Ñ‚Ð¾Ð±Ñ‹ Ð·Ð°Ð½Ð¸Ð¼Ð°Ð» ÑÑ‚Ñ€Ð¾ÐºÑƒ */
  margin-top: 10px;
}
.current-balance-display {
  display: flex;
  align-items: center;
  font-size: 24px; /* ÐšÑ€ÑƒÐ¿Ð½ÐµÐµ */
  font-weight: bold;
  margin-bottom: 5px;
  color: var(--app-primary-color);
}
.current-balance-display .large-icon {
  width: 28px;
  height: 28px;
  filter: brightness(0) saturate(100%) invert(69%) sepia(68%) saturate(497%)
    hue-rotate(83deg) brightness(99%) contrast(93%);
  background-size: calc(var(--sprite-total-width) / (var(--icon-width) / 28px))
    calc(var(--sprite-total-height) / (var(--icon-height) / 28px));
  margin-right: 10px;
}
.current-balance-display .balance-amount {
  margin-right: 5px;
}
.current-balance-display .balance-currency {
  font-size: 18px;
  color: var(--app-hint-color);
  font-weight: normal;
}
.withdrawal-form {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 15px;
}
.withdrawal-form label {
  font-size: 14px;
  color: var(--app-hint-color);
  margin-bottom: -8px;
}
.withdrawal-form input[type="number"],
.withdrawal-form input[type="text"] {
  padding: 12px 14px;
  border: 1px solid var(--app-separator-color);
  background-color: var(--app-bg-color);
  color: var(--app-text-color);
  border-radius: 10px;
  font-size: 16px;
  width: 100%;
}
.withdrawal-form input[type="number"]::-webkit-outer-spin-button,
.withdrawal-form input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
.withdrawal-form input[type="number"] {
  -moz-appearance: textfield;
  appearance: textfield;
}
.withdrawal-form button {
  margin-top: 10px;
}

/* --- ÐÐ¸Ð¶Ð½ÑÑ Ð½Ð°Ð²Ð¸Ð³Ð°Ñ†Ð¸Ñ --- */
.app-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgb(2 30 4 / 60%);
  display: flex;
  justify-content: space-around;
  padding: 5px 0;
  border-radius: 26px 26px 0px 0px;
  z-index: 100;
}
.nav-button {
  background: none;
  border: none;
  color: var(--app-hint-color);
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0px 0px 12px;
  font-size: 10px;
  flex-grow: 1;
  transition: color 0.2s ease, transform 0.1s ease;
}
.nav-button .nav-icon {
  width: 50px;
  height: 48px;
  margin-bottom: 2px;
  filter: brightness(0) invert(0.6);
  transition: filter 0.2s ease;
  background-size: calc(var(--sprite-total-width) / (var(--icon-width) / 24px))
    calc(var(--sprite-total-height) / (var(--icon-height) / 24px));
}
.nav-button.active .nav-icon {
  filter: brightness(0) saturate(100%) invert(69%) sepia(68%) saturate(497%)
    hue-rotate(83deg) brightness(99%) contrast(93%); /* Ð—ÐµÐ»ÐµÐ½Ñ‹Ð¹ Ð´Ð»Ñ Ð°ÐºÑ‚Ð¸Ð²Ð½Ð¾Ð¹ */
  background-image: url('./images/sprite.svg');
  background-size: 183px;
  background-position: 172px 55px;
}
.nav-button.active .nav-text {
  color: var(--app-text-color);
}
.nav-button:active:not(:disabled) {
  transform: scale(0.95);
}
.nav-button:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  transform: none;
}
.nav-button:disabled .nav-icon {
  filter: brightness(0) invert(0.4);
}

img.user-avatar-icon {
  width: 88%;
  border: 1px solid #06b706;
  border-radius: 50%;
}



/* Убираем стили для иконок в кнопках действий */




#nav-home > svg {
  /* saturate(497%) hue-rotate(83deg) brightness(99%) contrast(93%); */
  background-image: url('./images/sprite.svg');
  background-size: 142px;
  background-position: 141px 55px;
}
#nav-earn > svg {
    background-image: url('./images/sprite.svg');
    background-size: 142px;
    background-position: 95px 58px;
}

#nav-friends > svg {
    background-image: url('./images/sprite.svg');
    background-size: 142px;
    background-position: 49px 58px;
}

.app-container::before{
    content: "";
    position: absolute;
    top: 0px;
    bottom: 0px;
    left: 0px;
    right: 0px;
    width: 100%;
    height: 100%;
    background-image: url('./images/bg-1.svg');
    background-position: center;
    background-size: cover;
    opacity: 0.1;
}

.app-container::after{
    content: "";
    position: fixed;
    top: 255px;
    bottom: 0px;
    left: 0px;
    right: 0px;
    width: 100%;
    max-width: 350px;
    margin: 0px auto;
    height: auto;
    background-image: url('./images/bg-2.svg');
    background-position: center;
    background-size: 100%;
    background-repeat: no-repeat;
    opacity: 0.1;
}

/* === ТАБЫ === */
.tabs-container {
  margin-top: 15px;
}

.tabs-header {
  display: flex;
  background: rgba(60, 60, 62, 0.3);
  border-radius: 12px;
  padding: 4px;
  margin-bottom: 20px;
}

.tab-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 16px;
  border: none;
  background: transparent;
  color: var(--app-hint-color);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
}

.tab-button:hover {
  background: rgba(139, 92, 246, 0.1);
  color: var(--app-text-color);
}

.tab-button.active {
  background: var(--app-primary-color);
  color: white;
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
}

.tab-icon {
  font-size: 16px;
}

.tab-text {
  font-weight: 600;
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* === КАЛЬКУЛЯТОР === */
.calculator-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background: rgba(139, 92, 246, 0.1);
  border-radius: 12px;
  border: 1px solid rgba(139, 92, 246, 0.2);
}

.calculator-subtitle {
  margin: 0;
  color: var(--app-hint-color);
  font-size: 14px;
}

.balance-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.balance-label {
  color: var(--app-hint-color);
  font-size: 14px;
}

.balance-amount {
  color: var(--app-primary-color);
  font-weight: 600;
  font-size: 16px;
}

.amount-input-section {
  background: var(--app-secondary-bg-color);
  border: 1px solid #3c3c3e;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
}

.amount-input-section label {
  display: block;
  margin-bottom: 12px;
  font-weight: 600;
  color: var(--app-text-color);
  font-size: 16px;
}

.input-group {
  position: relative;
  margin-bottom: 12px;
}

.input-group input {
  width: 100%;
  padding: 16px 80px 16px 16px;
  border: 2px solid #3c3c3e;
  border-radius: 12px;
  font-size: 18px;
  font-weight: 600;
  box-sizing: border-box;
  background: var(--app-bg-color);
  color: var(--app-text-color);
  transition: all 0.3s ease;
}

.input-group input:focus {
  border-color: var(--app-primary-color);
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
  outline: none;
}

.input-suffix {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--app-hint-color);
  font-size: 14px;
  font-weight: 500;
  pointer-events: none;
}

.amount-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

#dollar-equivalent {
  font-weight: 600;
  color: var(--app-primary-color);
  font-size: 16px;
}

.balance-status {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

.balance-status.sufficient {
  background: rgba(76, 175, 80, 0.2);
  color: #4caf50;
}

.balance-status.insufficient {
  background: rgba(255, 107, 107, 0.2);
  color: #ff6b6b;
}

.balance-status.neutral {
  background: rgba(158, 158, 158, 0.2);
  color: var(--app-hint-color);
}

/* === СЕТКА ВАЛЮТ === */
.calculator-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.currency-card {
  background: var(--app-secondary-bg-color);
  border: 2px solid #3c3c3e;
  border-radius: 16px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.currency-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, transparent, var(--app-primary-color), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.currency-card:hover {
  border-color: var(--app-primary-color);
  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.15);
  transform: translateY(-2px);
}

.currency-card:hover::before {
  opacity: 1;
}

.currency-card.selected {
  border-color: #4caf50;
  background: rgba(76, 175, 80, 0.05);
  box-shadow: 0 8px 25px rgba(76, 175, 80, 0.2);
}

.currency-card.selected::before {
  background: linear-gradient(90deg, transparent, #4caf50, transparent);
  opacity: 1;
}

.currency-card.insufficient {
  border-color: #ff6b6b;
  background: rgba(255, 107, 107, 0.05);
  opacity: 0.6;
  cursor: not-allowed;
}

.currency-card.insufficient:hover {
  transform: none;
  box-shadow: none;
}

.currency-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.currency-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.currency-name {
  font-weight: 700;
  font-size: 18px;
  color: var(--app-text-color);
  line-height: 1.2;
}

.currency-symbol {
  font-size: 12px;
  color: var(--app-hint-color);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.currency-status {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-best {
  background: linear-gradient(135deg, #8b5cf6, #a855f7);
  color: white;
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
}

.status-good {
  background: rgba(76, 175, 80, 0.2);
  color: #4caf50;
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.status-expensive {
  background: rgba(255, 107, 107, 0.2);
  color: #ff6b6b;
  border: 1px solid rgba(255, 107, 107, 0.3);
}

.currency-details {
  font-size: 14px;
}

.min-requirement, .network-fee {
  color: var(--app-hint-color);
  margin-bottom: 8px;
  font-size: 13px;
}

.min-requirement {
  font-weight: 500;
}

.network-fee {
  font-weight: 600;
  color: #ff9800;
}

.calculation {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid rgba(60, 60, 62, 0.5);
}

.result-amount {
  font-weight: 700;
  font-size: 16px;
  color: #4caf50;
  margin-bottom: 6px;
}

.result-amount .amount {
  color: var(--app-primary-color);
}

.efficiency {
  font-size: 12px;
  color: var(--app-hint-color);
  margin-bottom: 12px;
}

.action-status {
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-align: center;
  margin-top: 12px;
}

.action-status.available {
  background: rgba(76, 175, 80, 0.2);
  color: #4caf50;
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.action-status.insufficient-funds {
  background: rgba(255, 107, 107, 0.2);
  color: #ff6b6b;
  border: 1px solid rgba(255, 107, 107, 0.3);
}

.action-status.insufficient-minimum {
  background: rgba(255, 152, 0, 0.2);
  color: #ff9800;
  border: 1px solid rgba(255, 152, 0, 0.3);
}

.action-status.loss {
  background: rgba(156, 39, 176, 0.2);
  color: #9c27b0;
  border: 1px solid rgba(156, 39, 176, 0.3);
}

.action-status.neutral {
  background: rgba(158, 158, 158, 0.2);
  color: var(--app-hint-color);
  border: 1px solid rgba(158, 158, 158, 0.3);
}

/* === ИСТОРИЯ ВЫПЛАТ === */
.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background: rgba(139, 92, 246, 0.1);
  border-radius: 12px;
  border: 1px solid rgba(139, 92, 246, 0.2);
}

.history-header h4 {
  margin: 0;
  color: var(--app-text-color);
  font-size: 18px;
  font-weight: 600;
}

.refresh-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: var(--app-primary-color);
  color: white;
  border: none;
  border-radius: 20px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.refresh-button:hover {
  background: #7c3aed;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.refresh-button:active {
  transform: translateY(0);
}

.refresh-icon {
  font-size: 14px;
  animation: none;
}

.refresh-button.loading .refresh-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.history-content {
  max-height: 400px;
  overflow-y: auto;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.history-placeholder {
  text-align: center;
  padding: 40px 20px;
  color: var(--app-hint-color);
}

.placeholder-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.placeholder-text {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--app-text-color);
}

.placeholder-hint {
  font-size: 14px;
  opacity: 0.7;
}

.history-item {
  background: var(--app-secondary-bg-color);
  border: 1px solid #3c3c3e;
  border-radius: 12px;
  padding: 16px;
  transition: all 0.3s ease;
}

.history-item:hover {
  border-color: var(--app-primary-color);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.1);
}

.withdrawal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.withdrawal-amount {
  font-weight: 700;
  font-size: 16px;
  color: var(--app-text-color);
}

.withdrawal-status {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.withdrawal-status.pending {
  background: rgba(255, 152, 0, 0.2);
  color: #ff9800;
  border: 1px solid rgba(255, 152, 0, 0.3);
}

.withdrawal-status.processing {
  background: rgba(33, 150, 243, 0.2);
  color: #2196f3;
  border: 1px solid rgba(33, 150, 243, 0.3);
}

.withdrawal-status.completed {
  background: rgba(76, 175, 80, 0.2);
  color: #4caf50;
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.withdrawal-status.failed {
  background: rgba(255, 107, 107, 0.2);
  color: #ff6b6b;
  border: 1px solid rgba(255, 107, 107, 0.3);
}

.withdrawal-status.cancelled {
  background: rgba(158, 158, 158, 0.2);
  color: var(--app-hint-color);
  border: 1px solid rgba(158, 158, 158, 0.3);
}

.withdrawal-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  font-size: 13px;
  color: var(--app-hint-color);
}

.withdrawal-currency {
  font-weight: 600;
  color: var(--app-primary-color);
  text-transform: uppercase;
}

.withdrawal-date {
  text-align: right;
}

.withdrawal-address {
  grid-column: 1 / -1;
  font-family: monospace;
  font-size: 11px;
  background: rgba(60, 60, 62, 0.3);
  padding: 8px;
  border-radius: 6px;
  word-break: break-all;
  margin-top: 8px;
}

/* Скроллбар для истории */
.history-content::-webkit-scrollbar {
  width: 6px;
}

.history-content::-webkit-scrollbar-track {
  background: rgba(60, 60, 62, 0.3);
  border-radius: 3px;
}

.history-content::-webkit-scrollbar-thumb {
  background: var(--app-primary-color);
  border-radius: 3px;
}

.history-content::-webkit-scrollbar-thumb:hover {
  background: #7c3aed;
}

/* === АНИМАЦИИ === */
@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* === АДАПТИВНОСТЬ === */
@media (max-width: 768px) {
  .calculator-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .currency-card {
    padding: 16px;
  }

  .tabs-header {
    padding: 3px;
  }

  .tab-button {
    padding: 10px 12px;
    font-size: 13px;
  }

  .tab-icon {
    font-size: 14px;
  }

  .calculator-header {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }

  .input-group input {
    font-size: 16px;
    padding: 14px 70px 14px 14px;
  }

  .currency-name {
    font-size: 16px;
  }

  .withdrawal-details {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .withdrawal-date {
    text-align: left;
  }
}