<?php
/**
 * api/admin/settings.php
 * Страница настроек административной панели
 */

// Включаем логирование
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../error.log');
error_reporting(E_ALL);

// Подключаем файл с функциями аутентификации и авторизации
require_once __DIR__ . '/auth.php';

// Проверяем аутентификацию
session_start();
if (!isAuthenticated()) {
    // Если пользователь не аутентифицирован, перенаправляем на страницу входа
    header('Location: login.php');
    exit;
}

// --- Подключение зависимостей с проверкой ---
if (!(@require_once __DIR__ . '/../config.php')) {
    http_response_code(500);
    error_log('FATAL: config.php not found in admin/settings.php');
    die('Ошибка: Не удалось загрузить config.php');
}
// --- Конец проверки зависимостей ---

// Обработка формы изменения учетных данных
$credentialsMessage = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'change_credentials') {
    if (isset($_POST['new_username']) && isset($_POST['new_password']) && isset($_POST['confirm_password'])) {
        $newUsername = trim($_POST['new_username']);
        $newPassword = $_POST['new_password'];
        $confirmPassword = $_POST['confirm_password'];

        if (empty($newUsername)) {
            $credentialsMessage = 'Ошибка: Имя пользователя не может быть пустым';
        } elseif (empty($newPassword)) {
            $credentialsMessage = 'Ошибка: Пароль не может быть пустым';
        } elseif ($newPassword !== $confirmPassword) {
            $credentialsMessage = 'Ошибка: Пароли не совпадают';
        } else {
            if (changeAdminCredentials($newUsername, $newPassword)) {
                $credentialsMessage = 'Учетные данные успешно изменены';

                // Обновляем имя пользователя в сессии
                $_SESSION['admin_username'] = $newUsername;
            } else {
                $credentialsMessage = 'Ошибка: Не удалось изменить учетные данные';
            }
        }
    } else {
        $credentialsMessage = 'Ошибка: Не все поля заполнены';
    }
}

// Обработка формы изменения настроек приложения
$settingsMessage = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'change_settings') {
    // Получаем текущие настройки из config.php
    $configFile = __DIR__ . '/../config.php';
    $configContent = file_get_contents($configFile);

    if ($configContent !== false) {
        $updated = false;

        // Обновляем AD_VIEW_REWARD
        if (isset($_POST['ad_view_reward']) && is_numeric($_POST['ad_view_reward'])) {
            $adViewReward = intval($_POST['ad_view_reward']);
            $pattern = "/define\('AD_VIEW_REWARD',\s*\d+\);/";
            $replacement = "define('AD_VIEW_REWARD', $adViewReward);";
            $configContent = preg_replace($pattern, $replacement, $configContent);
            $updated = true;
        }

        // Обновляем REFERRAL_BONUS_PERCENT
        if (isset($_POST['referral_bonus_percent']) && is_numeric($_POST['referral_bonus_percent'])) {
            $referralBonusPercent = floatval($_POST['referral_bonus_percent']) / 100;
            $pattern = "/define\('REFERRAL_BONUS_PERCENT',\s*[\d\.]+\);/";
            $replacement = "define('REFERRAL_BONUS_PERCENT', $referralBonusPercent);";
            $configContent = preg_replace($pattern, $replacement, $configContent);
            $updated = true;
        }

        // Обновляем MIN_WITHDRAWAL_AMOUNT
        if (isset($_POST['min_withdrawal_amount']) && is_numeric($_POST['min_withdrawal_amount'])) {
            $minWithdrawalAmount = intval($_POST['min_withdrawal_amount']);
            $pattern = "/define\('MIN_WITHDRAWAL_AMOUNT',\s*\d+\);/";
            $replacement = "define('MIN_WITHDRAWAL_AMOUNT', $minWithdrawalAmount);";
            $configContent = preg_replace($pattern, $replacement, $configContent);
            $updated = true;
        }

        // Обновляем MIN_BALANCE_FOR_WITHDRAWAL
        if (isset($_POST['min_balance_for_withdrawal']) && is_numeric($_POST['min_balance_for_withdrawal'])) {
            $minBalanceForWithdrawal = intval($_POST['min_balance_for_withdrawal']);
            $pattern = "/define\('MIN_BALANCE_FOR_WITHDRAWAL',\s*\d+\);/";
            $replacement = "define('MIN_BALANCE_FOR_WITHDRAWAL', $minBalanceForWithdrawal);";
            $configContent = preg_replace($pattern, $replacement, $configContent);
            $updated = true;
        }

        // Обновляем CONVERSION_RATE
        if (isset($_POST['conversion_rate']) && is_numeric($_POST['conversion_rate'])) {
            $conversionRate = floatval($_POST['conversion_rate']) / 100;
            $pattern = "/define\('CONVERSION_RATE',\s*[\d\.]+\);/";
            $replacement = "define('CONVERSION_RATE', $conversionRate);";
            $configContent = preg_replace($pattern, $replacement, $configContent);
            $updated = true;
        }

        // Обновляем NOWPAYMENTS_EMAIL
        if (isset($_POST['nowpayments_email']) && !empty($_POST['nowpayments_email'])) {
            $email = addslashes($_POST['nowpayments_email']);
            $pattern = "/define\('NOWPAYMENTS_EMAIL',\s*'[^']*'\);/";
            $replacement = "define('NOWPAYMENTS_EMAIL', '$email');";
            $configContent = preg_replace($pattern, $replacement, $configContent);
            $updated = true;
        }

        // Обновляем NOWPAYMENTS_PASSWORD
        if (isset($_POST['nowpayments_password']) && !empty($_POST['nowpayments_password'])) {
            $password = addslashes($_POST['nowpayments_password']);
            $pattern = "/define\('NOWPAYMENTS_PASSWORD',\s*'[^']*'\);/";
            $replacement = "define('NOWPAYMENTS_PASSWORD', '$password');";
            $configContent = preg_replace($pattern, $replacement, $configContent);
            $updated = true;
        }

        // Обновляем NOWPAYMENTS_API_KEY
        if (isset($_POST['nowpayments_api_key']) && !empty($_POST['nowpayments_api_key'])) {
            $apiKey = addslashes($_POST['nowpayments_api_key']);
            $pattern = "/define\('NOWPAYMENTS_API_KEY',\s*'[^']*'\);/";
            $replacement = "define('NOWPAYMENTS_API_KEY', '$apiKey');";
            $configContent = preg_replace($pattern, $replacement, $configContent);
            $updated = true;
        }

        // Обновляем NOWPAYMENTS_PUBLIC_KEY
        if (isset($_POST['nowpayments_public_key']) && !empty($_POST['nowpayments_public_key'])) {
            $publicKey = addslashes($_POST['nowpayments_public_key']);
            $pattern = "/define\('NOWPAYMENTS_PUBLIC_KEY',\s*'[^']*'\);/";
            $replacement = "define('NOWPAYMENTS_PUBLIC_KEY', '$publicKey');";
            $configContent = preg_replace($pattern, $replacement, $configContent);
            $updated = true;
        }

        // Обновляем NOWPAYMENTS_IPN_SECRET
        if (isset($_POST['nowpayments_ipn_secret']) && !empty($_POST['nowpayments_ipn_secret'])) {
            $ipnSecret = addslashes($_POST['nowpayments_ipn_secret']);
            $pattern = "/define\('NOWPAYMENTS_IPN_SECRET',\s*'[^']*'\);/";
            $replacement = "define('NOWPAYMENTS_IPN_SECRET', '$ipnSecret');";
            $configContent = preg_replace($pattern, $replacement, $configContent);
            $updated = true;
        }

        // Сохраняем обновленный файл конфигурации
        if ($updated) {
            if (file_put_contents($configFile, $configContent) !== false) {
                $settingsMessage = 'Настройки успешно обновлены';

                // Перезагружаем config.php
                require_once __DIR__ . '/../config.php';
            } else {
                $settingsMessage = 'Ошибка: Не удалось сохранить настройки';
            }
        } else {
            $settingsMessage = 'Ошибка: Не удалось обновить настройки';
        }
    } else {
        $settingsMessage = 'Ошибка: Не удалось прочитать файл конфигурации';
    }
}

// Подключаем шаблон заголовка
include 'templates/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Боковое меню -->
        <nav id="sidebar" class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
            <div class="position-sticky pt-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="bi bi-speedometer2 me-2"></i>
                            Панель управления
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="users.php">
                            <i class="bi bi-people me-2"></i>
                            Пользователи
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="stats.php">
                            <i class="bi bi-bar-chart me-2"></i>
                            Статистика
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="security.php">
                            <i class="bi bi-shield-lock me-2"></i>
                            Безопасность
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="settings.php">
                            <i class="bi bi-gear me-2"></i>
                            Настройки
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="logout.php">
                            <i class="bi bi-box-arrow-right me-2"></i>
                            Выход
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- Основное содержимое -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Настройки</h1>
            </div>

            <div class="row">
                <!-- Настройки учетных данных -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Учетные данные администратора</h5>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($credentialsMessage)): ?>
                                <div class="alert <?php echo strpos($credentialsMessage, 'Ошибка:') === 0 ? 'alert-danger' : 'alert-success'; ?> alert-dismissible fade show" role="alert">
                                    <?php echo $credentialsMessage; ?>
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            <?php endif; ?>

                            <form method="post">
                                <input type="hidden" name="action" value="change_credentials">

                                <div class="mb-3">
                                    <label for="new_username" class="form-label">Новое имя пользователя</label>
                                    <input type="text" class="form-control" id="new_username" name="new_username" value="<?php echo $_SESSION['admin_username']; ?>" required>
                                </div>

                                <div class="mb-3">
                                    <label for="new_password" class="form-label">Новый пароль</label>
                                    <input type="password" class="form-control" id="new_password" name="new_password" required>
                                </div>

                                <div class="mb-3">
                                    <label for="confirm_password" class="form-label">Подтверждение пароля</label>
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                </div>

                                <button type="submit" class="btn btn-primary">Сохранить</button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Настройки приложения -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Настройки приложения</h5>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($settingsMessage)): ?>
                                <div class="alert <?php echo strpos($settingsMessage, 'Ошибка:') === 0 ? 'alert-danger' : 'alert-success'; ?> alert-dismissible fade show" role="alert">
                                    <?php echo $settingsMessage; ?>
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            <?php endif; ?>

                            <form method="post">
                                <input type="hidden" name="action" value="change_settings">

                                <div class="mb-3">
                                    <label for="ad_view_reward" class="form-label">Награда за просмотр рекламы (монеты)</label>
                                    <input type="number" class="form-control" id="ad_view_reward" name="ad_view_reward" value="<?php echo defined('AD_VIEW_REWARD') ? AD_VIEW_REWARD : 10; ?>" min="1" required>
                                </div>

                                <div class="mb-3">
                                    <label for="referral_bonus_percent" class="form-label">Процент реферального бонуса (%)</label>
                                    <input type="number" class="form-control" id="referral_bonus_percent" name="referral_bonus_percent" value="<?php echo defined('REFERRAL_BONUS_PERCENT') ? REFERRAL_BONUS_PERCENT * 100 : 10; ?>" min="0" max="100" step="0.1" required>
                                </div>

                                <div class="mb-3">
                                    <label for="min_withdrawal_amount" class="form-label">Минимальная сумма для вывода (монеты)</label>
                                    <input type="number" class="form-control" id="min_withdrawal_amount" name="min_withdrawal_amount" value="<?php echo defined('MIN_WITHDRAWAL_AMOUNT') ? MIN_WITHDRAWAL_AMOUNT : 0; ?>" min="0" required>
                                </div>

                                <div class="mb-3">
                                    <label for="min_balance_for_withdrawal" class="form-label">Минимальный баланс для доступа к выводу (монеты)</label>
                                    <input type="number" class="form-control" id="min_balance_for_withdrawal" name="min_balance_for_withdrawal" value="<?php echo defined('MIN_BALANCE_FOR_WITHDRAWAL') ? MIN_BALANCE_FOR_WITHDRAWAL : 100; ?>" min="1" required>
                                    <div class="form-text">Пользователь сможет выводить средства только при наличии этого минимального баланса</div>
                                </div>

                                <div class="mb-3">
                                    <label for="conversion_rate" class="form-label">Курс конвертации монет в USD (%)</label>
                                    <input type="number" class="form-control" id="conversion_rate" name="conversion_rate" value="<?php echo defined('CONVERSION_RATE') ? CONVERSION_RATE * 100 : 1; ?>" min="0.01" step="0.01" required>
                                    <div class="form-text">1 монета = <span id="conversion_preview"><?php echo defined('CONVERSION_RATE') ? CONVERSION_RATE : 0.01; ?></span> USD</div>
                                </div>

                                <button type="submit" class="btn btn-primary">Сохранить</button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Настройки NOWPayments API -->
                <div class="col-md-12 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Настройки NOWPayments API</h5>
                        </div>
                        <div class="card-body">
                            <form method="post">
                                <input type="hidden" name="action" value="change_settings">

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="nowpayments_email" class="form-label">Email аккаунта NOWPayments</label>
                                            <input type="email" class="form-control" id="nowpayments_email" name="nowpayments_email" value="<?php echo defined('NOWPAYMENTS_EMAIL') ? NOWPAYMENTS_EMAIL : ''; ?>" required>
                                            <div class="form-text">Email для входа в панель NOWPayments</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="nowpayments_password" class="form-label">Пароль аккаунта NOWPayments</label>
                                            <input type="password" class="form-control" id="nowpayments_password" name="nowpayments_password" value="<?php echo defined('NOWPAYMENTS_PASSWORD') ? NOWPAYMENTS_PASSWORD : ''; ?>" required>
                                            <div class="form-text">Пароль для входа в панель NOWPayments</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="nowpayments_api_key" class="form-label">API Key (приватный)</label>
                                            <input type="text" class="form-control" id="nowpayments_api_key" name="nowpayments_api_key" value="<?php echo defined('NOWPAYMENTS_API_KEY') ? NOWPAYMENTS_API_KEY : ''; ?>" required>
                                            <div class="form-text">Приватный ключ для создания выплат</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="nowpayments_public_key" class="form-label">Public Key</label>
                                            <input type="text" class="form-control" id="nowpayments_public_key" name="nowpayments_public_key" value="<?php echo defined('NOWPAYMENTS_PUBLIC_KEY') ? NOWPAYMENTS_PUBLIC_KEY : ''; ?>" required>
                                            <div class="form-text">Публичный ключ для получения оценок</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="nowpayments_ipn_secret" class="form-label">IPN Secret Key</label>
                                    <input type="text" class="form-control" id="nowpayments_ipn_secret" name="nowpayments_ipn_secret" value="<?php echo defined('NOWPAYMENTS_IPN_SECRET') ? NOWPAYMENTS_IPN_SECRET : ''; ?>" required>
                                    <div class="form-text">Секретный ключ для проверки подписи callback уведомлений</div>
                                </div>

                                <div class="alert alert-info">
                                    <i class="bi bi-info-circle me-2"></i>
                                    <strong>Важно:</strong> Все ключи можно найти в панели управления NOWPayments.
                                    Email и пароль используются для автоматического получения JWT токенов.
                                </div>

                                <button type="submit" class="btn btn-primary">Сохранить настройки NOWPayments</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
    // Обновление предпросмотра курса конвертации
    document.getElementById('conversion_rate').addEventListener('input', function() {
        const rate = parseFloat(this.value) / 100;
        document.getElementById('conversion_preview').textContent = rate.toFixed(4);
    });
</script>

<?php
// Подключаем шаблон подвала
include 'templates/footer.php';
?>
