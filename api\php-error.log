[28-May-2025 11:34:08 UTC] requestWithdrawal INFO: Получен запрос на вывод 4 монет в usdttrc20 на адрес TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK
[28-May-2025 11:34:08 UTC] requestWithdrawal INFO: initData валидирован для user 5880288830
[28-May-2025 11:34:08 UTC] loadUserData INFO: Загрузка данных из /var/www/html/test2/api/user_data.json
[28-May-2025 11:34:08 UTC] loadUserData INFO: Данные успешно загружены.
[28-May-2025 11:34:08 UTC] requestWithdrawal INFO: Конвертация 4 монет в USD: 0.04
[28-May-2025 11:34:08 UTC] createWithdrawalRequest INFO: Начало создания запроса на вывод для пользователя 5880288830
[28-May-2025 11:34:08 UTC] createWithdrawalRequest INFO: Параметры - Amount: 0.04 USD, Currency: usdttrc20, Address: TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK
[28-May-2025 11:34:08 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.04&currency_from=usd&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[28-May-2025 11:34:08 UTC] createWithdrawalRequest INFO: Конвертация 0.04 USD в 0.039936 usdttrc20
[28-May-2025 11:34:08 UTC] NOWPaymentsAPI INFO: Пробуем endpoint: https://api.nowpayments.io/v1/payout-withdrawal
[28-May-2025 11:34:08 UTC] NOWPaymentsAPI INFO: Данные запроса: {"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":"0.039936","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}
[28-May-2025 11:34:08 UTC] NOWPaymentsAPI INFO: Пробуем метод авторизации: both
[28-May-2025 11:34:08 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[28-May-2025 11:34:08 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[28-May-2025 11:34:08 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[28-May-2025 11:34:08 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg0MzIwNDgsImV4cCI6MTc0ODQzMjM0OH0.h_1XnqRY3m-i9ExJGBEzMOjcRongENCbXrLStsvNhm0"}
[28-May-2025 11:34:08 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[28-May-2025 11:34:08 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg0MzIwNDgsImV4cCI6MTc0ODQzMjM0OH0.h_1XnqRY3m-i9ExJGBEzMOjcRongENCbXrLStsvNhm0","Content-Type: application\/json"]
[28-May-2025 11:34:09 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[28-May-2025 11:34:09 UTC] NOWPaymentsAPI INFO: Комбинация https://api.nowpayments.io/v1/payout-withdrawal + both не сработала
[28-May-2025 11:34:09 UTC] NOWPaymentsAPI INFO: Пробуем метод авторизации: bearer
[28-May-2025 11:34:09 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg0MzIwNDgsImV4cCI6MTc0ODQzMjM0OH0.h_1XnqRY3m-i9ExJGBEzMOjcRongENCbXrLStsvNhm0","Content-Type: application\/json"]
[28-May-2025 11:34:09 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[28-May-2025 11:34:09 UTC] NOWPaymentsAPI INFO: Комбинация https://api.nowpayments.io/v1/payout-withdrawal + bearer не сработала
[28-May-2025 11:34:09 UTC] NOWPaymentsAPI INFO: Пробуем метод авторизации: x-api-key
[28-May-2025 11:34:09 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout-withdrawal с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[28-May-2025 11:34:09 UTC] NOWPaymentsAPI HTTP INFO: Code 404, Response: {"status":false,"statusCode":404,"message":"Endpoint not found"}
[28-May-2025 11:34:09 UTC] NOWPaymentsAPI INFO: Комбинация https://api.nowpayments.io/v1/payout-withdrawal + x-api-key не сработала
[28-May-2025 11:34:09 UTC] NOWPaymentsAPI INFO: Пробуем endpoint: https://api.nowpayments.io/v1/payout
[28-May-2025 11:34:09 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":"0.039936","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[28-May-2025 11:34:09 UTC] NOWPaymentsAPI INFO: Пробуем метод авторизации: both
[28-May-2025 11:34:09 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg0MzIwNDgsImV4cCI6MTc0ODQzMjM0OH0.h_1XnqRY3m-i9ExJGBEzMOjcRongENCbXrLStsvNhm0","Content-Type: application\/json"]
[28-May-2025 11:34:10 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"usdttrc20":{"requested":0.039936,"actualBalance":0}}}
[28-May-2025 11:34:10 UTC] NOWPaymentsAPI INFO: Комбинация https://api.nowpayments.io/v1/payout + both не сработала
[28-May-2025 11:34:10 UTC] NOWPaymentsAPI INFO: Пробуем метод авторизации: bearer
[28-May-2025 11:34:10 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg0MzIwNDgsImV4cCI6MTc0ODQzMjM0OH0.h_1XnqRY3m-i9ExJGBEzMOjcRongENCbXrLStsvNhm0","Content-Type: application\/json"]
[28-May-2025 11:34:10 UTC] NOWPaymentsAPI HTTP INFO: Code 403, Response: {"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}
[28-May-2025 11:34:10 UTC] NOWPaymentsAPI INFO: Комбинация https://api.nowpayments.io/v1/payout + bearer не сработала
[28-May-2025 11:34:10 UTC] NOWPaymentsAPI INFO: Пробуем метод авторизации: x-api-key
[28-May-2025 11:34:10 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[28-May-2025 11:34:10 UTC] NOWPaymentsAPI HTTP INFO: Code 401, Response: {"status":false,"statusCode":401,"code":"AUTH_REQUIRED","message":"Authorization header is empty (Bearer JWTtoken is required)"}
[28-May-2025 11:34:10 UTC] NOWPaymentsAPI INFO: Комбинация https://api.nowpayments.io/v1/payout + x-api-key не сработала
[28-May-2025 11:34:10 UTC] NOWPaymentsAPI ERROR: Все endpoints и методы авторизации не сработали
[28-May-2025 11:34:10 UTC] createWithdrawalRequest ERROR: Не удалось создать выплату. Ответ API: false
[28-May-2025 11:34:10 UTC] requestWithdrawal ERROR: Не удалось создать запрос на вывод для пользователя 5880288830
[28-May-2025 11:40:41 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[28-May-2025 11:40:42 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[28-May-2025 11:40:43 UTC] NOWPaymentsAPI INFO: Проверка баланса для поиска валюты с достаточными средствами
[28-May-2025 11:40:43 UTC] NOWPaymentsAPI INFO: Минимальная сумма: 1.0E-5
[28-May-2025 11:40:43 UTC] NOWPaymentsAPI INFO: Найдена валюта btc с балансом 2.6E-5
[28-May-2025 11:42:30 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[28-May-2025 11:42:30 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"**********************************","currency":"btc","amount":1.0e-5,"ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[28-May-2025 11:42:30 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[28-May-2025 11:42:30 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[28-May-2025 11:42:30 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[28-May-2025 11:42:30 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg0MzI1NDcsImV4cCI6MTc0ODQzMjg0N30.wbJTZNS2br-st223KlL9EceDPjUDrOUzYrJGa0q6hwA"}
[28-May-2025 11:42:30 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[28-May-2025 11:42:30 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg0MzI1NDcsImV4cCI6MTc0ODQzMjg0N30.wbJTZNS2br-st223KlL9EceDPjUDrOUzYrJGa0q6hwA","Content-Type: application\/json"]
[28-May-2025 11:42:32 UTC] NOWPaymentsAPI SUCCESS: Выплата создана успешно
[28-May-2025 11:42:32 UTC] NOWPaymentsAPI INFO: Ответ API: {"id":"5003087125","withdrawals":[{"is_request_payouts":false,"id":"5003715097","address":"**********************************","currency":"btc","amount":"0.00001","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php","batch_withdrawal_id":"5003087125","status":"CREATING","error":null,"extra_id":null,"hash":null,"payout_description":null,"unique_external_id":null,"created_at":"2025-05-28T11:42:29.067Z","requested_at":null,"updated_at":null,"update_history_log":null,"rejected_check_attempts":0,"fee":null,"fee_paid_by":null}]}
[28-May-2025 11:42:34 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout/5003087125 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg0MzI1NDcsImV4cCI6MTc0ODQzMjg0N30.wbJTZNS2br-st223KlL9EceDPjUDrOUzYrJGa0q6hwA","Content-Type: application\/json"]
[28-May-2025 12:04:29 UTC] PHP Warning:  file_put_contents(/var/www/html/test2/bot/bot.log): Failed to open stream: Permission denied in /var/www/html/test2/bot/config.php on line 41
[28-May-2025 12:04:29 UTC] PHP Warning:  file_put_contents(/var/www/html/test2/bot/bot.log): Failed to open stream: Permission denied in /var/www/html/test2/bot/config.php on line 41
[28-May-2025 12:04:29 UTC] PHP Warning:  file_put_contents(/var/www/html/test2/bot/bot.log): Failed to open stream: Permission denied in /var/www/html/test2/bot/config.php on line 41
[28-May-2025 12:04:29 UTC] PHP Warning:  file_put_contents(/var/www/html/test2/bot/bot.log): Failed to open stream: Permission denied in /var/www/html/test2/bot/config.php on line 41
[28-May-2025 12:04:29 UTC] loadUserData INFO: Загрузка данных из /var/www/html/test2/bot/../api/user_data.json
[28-May-2025 12:04:29 UTC] loadUserData INFO: Данные успешно загружены.
[28-May-2025 12:04:29 UTC] saveUserData INFO: Начало сохранения данных.
[28-May-2025 12:04:29 UTC] saveUserData INFO: Структура данных для сохранения проверена.
[28-May-2025 12:04:29 UTC] saveUserData INFO: json_encode успешен.
[28-May-2025 12:04:29 UTC] saveUserData INFO: Права на запись проверены. Попытка file_put_contents.
[28-May-2025 12:04:29 UTC] saveUserData SUCCESS: Данные сохранены. Записано байт: 766
[28-May-2025 12:04:29 UTC] PHP Warning:  file_get_contents(https://api.telegram.org/bot8146437206:AAET9K-yAOukAr9gKuntU9IQds8DAEiAbnA/sendPhoto): Failed to open stream: HTTP request failed! HTTP/1.1 400 Bad Request
 in /var/www/html/test2/bot/config.php on line 59
[28-May-2025 12:04:29 UTC] PHP Warning:  file_put_contents(/var/www/html/test2/bot/bot.log): Failed to open stream: Permission denied in /var/www/html/test2/bot/config.php on line 41
[28-May-2025 12:04:34 UTC] PHP Warning:  file_put_contents(/var/www/html/test2/bot/bot.log): Failed to open stream: Permission denied in /var/www/html/test2/bot/config.php on line 41
[28-May-2025 12:04:34 UTC] PHP Warning:  file_put_contents(/var/www/html/test2/bot/bot.log): Failed to open stream: Permission denied in /var/www/html/test2/bot/config.php on line 41
[28-May-2025 12:04:34 UTC] loadUserData INFO: Загрузка данных из /var/www/html/test2/bot/../api/user_data.json
[28-May-2025 12:04:34 UTC] loadUserData INFO: Данные успешно загружены.
[28-May-2025 12:04:34 UTC] loadUserData INFO: Загрузка данных из /var/www/html/test2/bot/../api/user_data.json
[28-May-2025 12:04:34 UTC] loadUserData INFO: Данные успешно загружены.
[28-May-2025 12:04:34 UTC] PHP Warning:  file_put_contents(/var/www/html/test2/bot/bot.log): Failed to open stream: Permission denied in /var/www/html/test2/bot/config.php on line 41
[28-May-2025 12:04:50 UTC] PHP Warning:  file_put_contents(/var/www/html/test2/bot/bot.log): Failed to open stream: Permission denied in /var/www/html/test2/bot/config.php on line 41
[28-May-2025 12:04:50 UTC] PHP Warning:  file_put_contents(/var/www/html/test2/bot/bot.log): Failed to open stream: Permission denied in /var/www/html/test2/bot/config.php on line 41
[28-May-2025 12:04:50 UTC] loadUserData INFO: Загрузка данных из /var/www/html/test2/bot/../api/user_data.json
[28-May-2025 12:04:50 UTC] loadUserData INFO: Данные успешно загружены.
[28-May-2025 12:04:57 UTC] getUserData INFO: Получен initData (длина: 624)
[28-May-2025 12:04:57 UTC] getUserData INFO: initData успешно валидирован для пользователя 5880288830
[28-May-2025 12:04:57 UTC] loadUserData INFO: Загрузка данных из /var/www/html/test2/api/user_data.json
[28-May-2025 12:04:57 UTC] loadUserData INFO: Данные успешно загружены.
[28-May-2025 12:04:57 UTC] getUserData INFO: Данные пользователей загружены для user 5880288830.
[28-May-2025 12:04:57 UTC] db_mock INFO: Updated Telegram user data for user 5880288830
[28-May-2025 12:04:57 UTC] getUserData INFO: Детали пользователя 5880288830 получены. Баланс: 200
[28-May-2025 12:04:57 UTC] saveUserData INFO: Начало сохранения данных.
[28-May-2025 12:04:57 UTC] saveUserData INFO: Структура данных для сохранения проверена.
[28-May-2025 12:04:57 UTC] saveUserData INFO: json_encode успешен.
[28-May-2025 12:04:57 UTC] saveUserData INFO: Права на запись проверены. Попытка file_put_contents.
[28-May-2025 12:04:57 UTC] saveUserData SUCCESS: Данные сохранены. Записано байт: 826
[28-May-2025 12:04:57 UTC] getUserData INFO: Данные пользователя 5880288830 сохранены (на случай создания).
[28-May-2025 12:04:57 UTC] getUserData INFO: Успешный ответ отправлен для user 5880288830.
[28-May-2025 12:04:57 UTC] loadUserData INFO: Загрузка данных из /var/www/html/test2/api/user_data.json
[28-May-2025 12:04:57 UTC] loadUserData INFO: Данные успешно загружены.
[28-May-2025 12:04:57 UTC] getUserLanguage INFO: Определение языка для пользователя 5880288830
[28-May-2025 12:04:57 UTC] getUserLanguage INFO: Данные пользователя из Telegram: {"id":5880288830,"first_name":"\u0410\u043b\u044c\u0442\u0435\u0440","last_name":"\u042d\u0433\u043e","username":"alter_mega_ego","language_code":"ru","allows_write_to_pm":true,"photo_url":"https:\/\/t.me\/i\/userpic\/320\/oqepLENWEP9UvyQj4_1XP_D21AocO8M1_xVqdcAYyOik9TmM7SEWhy6lMaUFwryT.svg"}
[28-May-2025 12:04:57 UTC] getUserLanguage INFO: Автоматически определен язык для пользователя 5880288830: ru
[28-May-2025 12:04:57 UTC] saveUserData INFO: Начало сохранения данных.
[28-May-2025 12:04:57 UTC] saveUserData INFO: Структура данных для сохранения проверена.
[28-May-2025 12:04:57 UTC] saveUserData INFO: json_encode успешен.
[28-May-2025 12:04:57 UTC] saveUserData INFO: Права на запись проверены. Попытка file_put_contents.
[28-May-2025 12:04:57 UTC] saveUserData SUCCESS: Данные сохранены. Записано байт: 852
[28-May-2025 12:04:57 UTC] getUserLanguage INFO: Язык ru сохранен для пользователя 5880288830
[28-May-2025 12:05:02 UTC] getWithdrawalHistory INFO: Получен initData (длина: 624)
[28-May-2025 12:05:02 UTC] getWithdrawalHistory INFO: initData валидирован для user 5880288830
[28-May-2025 12:05:02 UTC] loadUserData INFO: Загрузка данных из /var/www/html/test2/api/user_data.json
[28-May-2025 12:05:02 UTC] loadUserData INFO: Данные успешно загружены.
[28-May-2025 12:05:02 UTC] getWithdrawalHistory INFO: Успешно отправлена история выплат для пользователя 5880288830 (всего: 0)
[28-May-2025 12:05:26 UTC] requestWithdrawal INFO: Получен запрос на вывод 4 монет в usdttrc20 на адрес TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK
[28-May-2025 12:05:26 UTC] requestWithdrawal INFO: initData валидирован для user 5880288830
[28-May-2025 12:05:26 UTC] loadUserData INFO: Загрузка данных из /var/www/html/test2/api/user_data.json
[28-May-2025 12:05:26 UTC] loadUserData INFO: Данные успешно загружены.
[28-May-2025 12:05:26 UTC] requestWithdrawal INFO: Конвертация 4 монет в USD: 0.04
[28-May-2025 12:05:26 UTC] createWithdrawalRequest INFO: Начало создания запроса на вывод для пользователя 5880288830
[28-May-2025 12:05:26 UTC] createWithdrawalRequest INFO: Параметры - Amount: 0.04 USD, Currency: usdttrc20, Address: TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK
[28-May-2025 12:05:26 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.04&currency_from=usd&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[28-May-2025 12:05:31 UTC] createWithdrawalRequest INFO: Конвертация 0.04 USD в 0.039959 usdttrc20
[28-May-2025 12:05:31 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[28-May-2025 12:05:31 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":"0.039959","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[28-May-2025 12:05:31 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[28-May-2025 12:05:31 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[28-May-2025 12:05:31 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[28-May-2025 12:05:31 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg0MzM5MzEsImV4cCI6MTc0ODQzNDIzMX0.KCWm2RCXLodQgYiM8uhYgaZ3NtLK8ck1T9Vokfl96Ok"}
[28-May-2025 12:05:31 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[28-May-2025 12:05:31 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg0MzM5MzEsImV4cCI6MTc0ODQzNDIzMX0.KCWm2RCXLodQgYiM8uhYgaZ3NtLK8ck1T9Vokfl96Ok","Content-Type: application\/json"]
[28-May-2025 12:05:33 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"usdttrc20":{"requested":0.039959,"actualBalance":0}}}
[28-May-2025 12:05:33 UTC] NOWPaymentsAPI ERROR: Не удалось создать выплату
[28-May-2025 12:05:33 UTC] createWithdrawalRequest ERROR: Не удалось создать выплату. Ответ API: false
[28-May-2025 12:05:33 UTC] requestWithdrawal ERROR: Не удалось создать запрос на вывод для пользователя 5880288830
[28-May-2025 12:13:41 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[28-May-2025 12:13:42 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[28-May-2025 12:13:42 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":0.1,"ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[28-May-2025 12:13:42 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[28-May-2025 12:13:42 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[28-May-2025 12:13:42 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[28-May-2025 12:13:43 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg0MzQ0MjAsImV4cCI6MTc0ODQzNDcyMH0.RY5sCB3Ed3RAHqcuZnPNv8B4xxIub0aMMdlhuqTps7g"}
[28-May-2025 12:13:43 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[28-May-2025 12:13:43 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg0MzQ0MjAsImV4cCI6MTc0ODQzNDcyMH0.RY5sCB3Ed3RAHqcuZnPNv8B4xxIub0aMMdlhuqTps7g","Content-Type: application\/json"]
[28-May-2025 12:13:45 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"usdttrc20":{"requested":0.1,"actualBalance":0}}}
[28-May-2025 12:13:45 UTC] NOWPaymentsAPI ERROR: Insufficient balance
[28-May-2025 12:13:45 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[28-May-2025 12:13:46 UTC] NOWPaymentsAPI INFO: Проверка баланса для поиска валюты с достаточными средствами
[28-May-2025 12:13:46 UTC] NOWPaymentsAPI INFO: Минимальная сумма: 1.0E-5
[28-May-2025 12:13:46 UTC] NOWPaymentsAPI INFO: Найдена валюта btc с балансом 2.6E-5
[28-May-2025 12:26:42 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[28-May-2025 12:26:43 UTC] NOWPaymentsAPI INFO: Попытка создания выплаты с автоконвертацией
[28-May-2025 12:26:43 UTC] NOWPaymentsAPI INFO: Цель - 4.0E-5 usdttrc20 на адрес TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK
[28-May-2025 12:26:43 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[28-May-2025 12:26:43 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":4.0e-5,"ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[28-May-2025 12:26:43 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[28-May-2025 12:26:43 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[28-May-2025 12:26:43 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[28-May-2025 12:26:44 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg0MzUyMDEsImV4cCI6MTc0ODQzNTUwMX0.Ur8nDdKCt0rcfNEYTDVOMs2BbZSi1tMevpceL99ehBY"}
[28-May-2025 12:26:44 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[28-May-2025 12:26:44 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg0MzUyMDEsImV4cCI6MTc0ODQzNTUwMX0.Ur8nDdKCt0rcfNEYTDVOMs2BbZSi1tMevpceL99ehBY","Content-Type: application\/json"]
[28-May-2025 12:26:46 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"usdttrc20":{"requested":0.00004,"actualBalance":0}}}
[28-May-2025 12:26:46 UTC] NOWPaymentsAPI ERROR: Insufficient balance
[28-May-2025 12:26:46 UTC] NOWPaymentsAPI INFO: Прямая выплата не удалась, пробуем автоконвертацию
[28-May-2025 12:26:46 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[28-May-2025 12:26:47 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[28-May-2025 12:26:48 UTC] NOWPaymentsAPI INFO: Найдена опция конвертации: 3.6776770660058E-10 btc -> 4.0E-5 usdttrc20
[28-May-2025 12:26:48 UTC] NOWPaymentsAPI INFO: Выбрана лучшая опция конвертации: 3.6776770660058E-10 btc
[28-May-2025 12:26:48 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[28-May-2025 12:26:48 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"btc","amount":3.677677066005816e-10,"ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[28-May-2025 12:26:48 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg0MzUyMDEsImV4cCI6MTc0ODQzNTUwMX0.Ur8nDdKCt0rcfNEYTDVOMs2BbZSi1tMevpceL99ehBY","Content-Type: application\/json"]
[28-May-2025 12:26:50 UTC] NOWPaymentsAPI SUCCESS: Выплата создана успешно
[28-May-2025 12:26:50 UTC] NOWPaymentsAPI INFO: Ответ API: {"id":"5003087236","withdrawals":[{"is_request_payouts":false,"id":"5003715281","address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"btc","amount":"0.0000000003677677066005816","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php","error":"Invalid payout address: BTC TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","status":"REJECTED","batch_withdrawal_id":"5003087236","extra_id":null,"hash":null,"payout_description":null,"unique_external_id":null,"created_at":"2025-05-28T12:26:47.549Z","requested_at":null,"updated_at":null,"update_history_log":null,"rejected_check_attempts":0,"fee":null,"fee_paid_by":null}]}
[28-May-2025 12:26:50 UTC] NOWPaymentsAPI SUCCESS: Выплата с автоконвертацией создана успешно
[28-May-2025 12:26:52 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout/5003087236 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg0MzUyMDEsImV4cCI6MTc0ODQzNTUwMX0.Ur8nDdKCt0rcfNEYTDVOMs2BbZSi1tMevpceL99ehBY","Content-Type: application\/json"]
[28-May-2025 12:26:53 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[28-May-2025 12:26:54 UTC] NOWPaymentsAPI INFO: Проверка баланса для поиска валюты с достаточными средствами
[28-May-2025 12:26:54 UTC] NOWPaymentsAPI INFO: Минимальная сумма: 1.0E-5
[28-May-2025 12:26:54 UTC] NOWPaymentsAPI INFO: Найдена валюта btc с балансом 2.6E-5
[28-May-2025 12:26:54 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1.0E-5&currency_from=btc&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[28-May-2025 12:26:54 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1.0E-5&currency_from=btc&currency_to=usdterc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[28-May-2025 12:26:55 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1.0E-5&currency_from=btc&currency_to=eth с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[28-May-2025 12:26:55 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1.0E-5&currency_from=btc&currency_to=trx с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[28-May-2025 12:26:56 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.04&currency_from=usd&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[28-May-2025 12:26:56 UTC] NOWPaymentsAPI INFO: Попытка создания выплаты с автоконвертацией
[28-May-2025 12:26:56 UTC] NOWPaymentsAPI INFO: Цель - 0.039946 usdttrc20 на адрес TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK
[28-May-2025 12:26:56 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[28-May-2025 12:26:56 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":"0.039946","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[28-May-2025 12:26:56 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg0MzUyMDEsImV4cCI6MTc0ODQzNTUwMX0.Ur8nDdKCt0rcfNEYTDVOMs2BbZSi1tMevpceL99ehBY","Content-Type: application\/json"]
[28-May-2025 12:26:58 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"usdttrc20":{"requested":0.039946,"actualBalance":0}}}
[28-May-2025 12:26:58 UTC] NOWPaymentsAPI ERROR: Insufficient balance
[28-May-2025 12:26:58 UTC] NOWPaymentsAPI INFO: Прямая выплата не удалась, пробуем автоконвертацию
[28-May-2025 12:26:58 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[28-May-2025 12:26:59 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[28-May-2025 12:27:00 UTC] NOWPaymentsAPI INFO: Найдена опция конвертации: 3.6714437568071E-7 btc -> 0.039946 usdttrc20
[28-May-2025 12:27:00 UTC] NOWPaymentsAPI INFO: Выбрана лучшая опция конвертации: 3.6714437568071E-7 btc
[28-May-2025 12:27:00 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[28-May-2025 12:27:00 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"btc","amount":3.671443756807097e-7,"ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[28-May-2025 12:27:00 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg0MzUyMDEsImV4cCI6MTc0ODQzNTUwMX0.Ur8nDdKCt0rcfNEYTDVOMs2BbZSi1tMevpceL99ehBY","Content-Type: application\/json"]
[28-May-2025 12:27:02 UTC] NOWPaymentsAPI SUCCESS: Выплата создана успешно
[28-May-2025 12:27:02 UTC] NOWPaymentsAPI INFO: Ответ API: {"id":"5003087238","withdrawals":[{"is_request_payouts":false,"id":"5003715283","address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"btc","amount":"0.0000003671443756807097","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php","error":"Invalid payout address: BTC TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","status":"REJECTED","batch_withdrawal_id":"5003087238","extra_id":null,"hash":null,"payout_description":null,"unique_external_id":null,"created_at":"2025-05-28T12:26:59.042Z","requested_at":null,"updated_at":null,"update_history_log":null,"rejected_check_attempts":0,"fee":null,"fee_paid_by":null}]}
[28-May-2025 12:27:02 UTC] NOWPaymentsAPI SUCCESS: Выплата с автоконвертацией создана успешно
[29-May-2025 14:05:36 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 14:05:37 UTC] PHP Warning:  Array to string conversion in D:\OSPanel\domains\argun-defolt.loc\test_payout_fix.php on line 46
[29-May-2025 14:05:37 UTC] NOWPaymentsAPI INFO: Автоконвертация usdttrc20 -> btc: используем тестовый адрес **********************************
[29-May-2025 14:05:37 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1&currency_from=usd&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 14:05:38 UTC] NOWPaymentsAPI INFO: Автоконвертация usdttrc20 -> btc: используем тестовый адрес **********************************
[29-May-2025 14:05:38 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1&currency_from=usd&currency_to=btc с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 14:05:39 UTC] PHP Warning:  Array to string conversion in D:\OSPanel\domains\argun-defolt.loc\test_payout_fix.php on line 88
[29-May-2025 14:05:39 UTC] NOWPaymentsAPI INFO: Автоконвертация eth -> btc: используем тестовый адрес **********************************
[29-May-2025 14:05:39 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=1&currency_from=usd&currency_to=eth с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 14:05:39 UTC] NOWPaymentsAPI INFO: Автоконвертация eth -> btc: используем тестовый адрес **********************************
[29-May-2025 14:05:39 UTC] PHP Warning:  Array to string conversion in D:\OSPanel\domains\argun-defolt.loc\test_payout_fix.php on line 139
[29-May-2025 14:05:39 UTC] NOWPaymentsAPI INFO: Автоконвертация usdttrc20 -> btc: используем тестовый адрес **********************************
[29-May-2025 14:05:39 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=0.01&currency_from=usdttrc20&currency_to=btc с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 14:06:27 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 14:07:17 UTC] NOWPaymentsAPI INFO: Автоконвертация usdttrc20 -> btc: используем тестовый адрес **********************************
[29-May-2025 14:07:17 UTC] NOWPaymentsAPI INFO: Автоконвертация eth -> btc: используем тестовый адрес **********************************
[29-May-2025 14:07:17 UTC] NOWPaymentsAPI INFO: Автоконвертация btc -> usdttrc20: используем тестовый адрес TLsV52sRDL79HXGGm9yzwKiW6SLtQ4KyUh
[29-May-2025 14:07:17 UTC] NOWPaymentsAPI INFO: Автоконвертация usdttrc20 -> btc: используем тестовый адрес **********************************
[29-May-2025 14:07:17 UTC] NOWPaymentsAPI INFO: Автоконвертация different -> btc: используем тестовый адрес **********************************
[29-May-2025 14:07:17 UTC] NOWPaymentsAPI INFO: Автоконвертация different -> eth: используем тестовый адрес ******************************************
[29-May-2025 14:07:17 UTC] NOWPaymentsAPI INFO: Автоконвертация different -> ltc: используем тестовый адрес LTC1QW508D6QEJXTDG4Y5R3ZARVARY0C5XW7KV8F3T4
[29-May-2025 14:07:17 UTC] NOWPaymentsAPI INFO: Автоконвертация different -> trx: используем тестовый адрес TLsV52sRDL79HXGGm9yzwKiW6SLtQ4KyUh
[29-May-2025 14:07:17 UTC] NOWPaymentsAPI INFO: Автоконвертация different -> usdttrc20: используем тестовый адрес TLsV52sRDL79HXGGm9yzwKiW6SLtQ4KyUh
[29-May-2025 14:07:17 UTC] NOWPaymentsAPI INFO: Автоконвертация different -> usdterc20: используем тестовый адрес ******************************************
[29-May-2025 14:07:17 UTC] NOWPaymentsAPI INFO: Автоконвертация different -> bnb: используем тестовый адрес bnb1grpf0955h0ykzq3ar5nmum7y6gdfl6lxfn46h2
[29-May-2025 14:07:17 UTC] NOWPaymentsAPI INFO: Автоконвертация different -> doge: используем тестовый адрес DH5yaieqoZN36fDVciNyRueRGvGLR3mr7L
[29-May-2025 14:12:06 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 14:12:08 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 14:12:08 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 14:12:08 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"**********************************","currency":"btc","amount":1.0e-5,"ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 14:12:08 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[29-May-2025 14:12:08 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[29-May-2025 14:12:08 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[29-May-2025 14:12:09 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1Mjc5MjYsImV4cCI6MTc0ODUyODIyNn0.SsGdjoLur57u6KPhr_W9GY-CxU0rQBVb5hPFghFAVZk"}
[29-May-2025 14:12:09 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[29-May-2025 14:12:09 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1Mjc5MjYsImV4cCI6MTc0ODUyODIyNn0.SsGdjoLur57u6KPhr_W9GY-CxU0rQBVb5hPFghFAVZk","Content-Type: application\/json"]
[29-May-2025 14:12:10 UTC] NOWPaymentsAPI SUCCESS: Выплата создана успешно
[29-May-2025 14:12:10 UTC] NOWPaymentsAPI INFO: Ответ API: {"id":"5003096013","withdrawals":[{"is_request_payouts":false,"id":"5003725204","address":"**********************************","currency":"btc","amount":"0.00001","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php","batch_withdrawal_id":"5003096013","status":"CREATING","error":null,"extra_id":null,"hash":null,"payout_description":null,"unique_external_id":null,"created_at":"2025-05-29T14:12:07.803Z","requested_at":null,"updated_at":null,"update_history_log":null,"rejected_check_attempts":0,"fee":null,"fee_paid_by":null}]}
[29-May-2025 14:12:14 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout/5003096013 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1Mjc5MjYsImV4cCI6MTc0ODUyODIyNn0.SsGdjoLur57u6KPhr_W9GY-CxU0rQBVb5hPFghFAVZk","Content-Type: application\/json"]
[29-May-2025 14:13:20 UTC] NOWPaymentsAPI INFO: Автоконвертация usdttrc20 -> btc: используем тестовый адрес **********************************
[29-May-2025 14:13:20 UTC] NOWPaymentsAPI INFO: Попытка создания выплаты с автоконвертацией
[29-May-2025 14:13:20 UTC] NOWPaymentsAPI INFO: Цель - 0.5 usdttrc20 на адрес TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK
[29-May-2025 14:13:20 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 14:13:20 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":"0.5","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 14:13:20 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[29-May-2025 14:13:20 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[29-May-2025 14:13:20 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[29-May-2025 14:13:20 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1Mjc5OTgsImV4cCI6MTc0ODUyODI5OH0.hI1eo5Zfb2sVwjs00_yC7H47n7tXRYjVcf_UIxDMNuQ"}
[29-May-2025 14:13:20 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[29-May-2025 14:13:20 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1Mjc5OTgsImV4cCI6MTc0ODUyODI5OH0.hI1eo5Zfb2sVwjs00_yC7H47n7tXRYjVcf_UIxDMNuQ","Content-Type: application\/json"]
[29-May-2025 14:13:22 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"usdttrc20":{"requested":0.5,"actualBalance":0}}}
[29-May-2025 14:13:22 UTC] NOWPaymentsAPI ERROR: Insufficient balance
[29-May-2025 14:13:22 UTC] NOWPaymentsAPI INFO: Прямая выплата не удалась, пробуем автоконвертацию
[29-May-2025 14:13:22 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 14:13:23 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 14:13:24 UTC] NOWPaymentsAPI INFO: Найдена опция конвертации: 4.6474531420537E-6 btc -> 0.5 usdttrc20
[29-May-2025 14:13:24 UTC] NOWPaymentsAPI INFO: Выбрана лучшая опция конвертации: 4.6474531420537E-6 btc
[29-May-2025 14:13:24 UTC] NOWPaymentsAPI INFO: Автоконвертация usdttrc20 -> btc: используем тестовый адрес **********************************
[29-May-2025 14:13:24 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 14:13:24 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"**********************************","currency":"btc","amount":4.647453142053695e-6,"ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 14:13:24 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1Mjc5OTgsImV4cCI6MTc0ODUyODI5OH0.hI1eo5Zfb2sVwjs00_yC7H47n7tXRYjVcf_UIxDMNuQ","Content-Type: application\/json"]
[29-May-2025 14:13:25 UTC] NOWPaymentsAPI SUCCESS: Выплата создана успешно
[29-May-2025 14:13:27 UTC] NOWPaymentsAPI INFO: Ответ API: {"id":"5003096016","withdrawals":[{"is_request_payouts":false,"id":"5003725207","address":"**********************************","currency":"btc","amount":"0.000004","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php","batch_withdrawal_id":"5003096016","status":"CREATING","error":null,"extra_id":null,"hash":null,"payout_description":null,"unique_external_id":null,"created_at":"2025-05-29T14:13:22.834Z","requested_at":null,"updated_at":null,"update_history_log":null,"rejected_check_attempts":0,"fee":null,"fee_paid_by":null}]}
[29-May-2025 14:13:27 UTC] NOWPaymentsAPI SUCCESS: Выплата с автоконвертацией создана успешно
[29-May-2025 14:13:30 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/payout/5003096016 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1Mjc5OTgsImV4cCI6MTc0ODUyODI5OH0.hI1eo5Zfb2sVwjs00_yC7H47n7tXRYjVcf_UIxDMNuQ","Content-Type: application\/json"]
[29-May-2025 14:50:57 UTC] NOWPaymentsAPI INFO: Попытка создания выплаты с автоконвертацией
[29-May-2025 14:50:57 UTC] NOWPaymentsAPI INFO: Цель - 0.1 usdttrc20 на адрес **********************************
[29-May-2025 14:50:57 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 14:50:57 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"**********************************","currency":"usdttrc20","amount":"0.1","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 14:50:57 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[29-May-2025 14:50:57 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[29-May-2025 14:50:57 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[29-May-2025 14:50:57 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzAyNTQsImV4cCI6MTc0ODUzMDU1NH0.Gje-9xmfYbAI9sp6_zK-qS7wD9cNed11-ztVQ71OnZU"}
[29-May-2025 14:50:57 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[29-May-2025 14:50:57 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzAyNTQsImV4cCI6MTc0ODUzMDU1NH0.Gje-9xmfYbAI9sp6_zK-qS7wD9cNed11-ztVQ71OnZU","Content-Type: application\/json"]
[29-May-2025 14:51:00 UTC] NOWPaymentsAPI SUCCESS: Выплата создана успешно
[29-May-2025 14:51:00 UTC] NOWPaymentsAPI INFO: Ответ API: {"id":"5003096105","withdrawals":[{"is_request_payouts":false,"id":"5003725327","address":"**********************************","currency":"usdttrc20","amount":"0.1","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php","error":"Invalid payout address: USDTTRC20 **********************************","status":"REJECTED","batch_withdrawal_id":"5003096105","extra_id":null,"hash":null,"payout_description":null,"unique_external_id":null,"created_at":"2025-05-29T14:50:57.006Z","requested_at":null,"updated_at":null,"update_history_log":null,"rejected_check_attempts":0,"fee":null,"fee_paid_by":null}]}
[29-May-2025 14:51:00 UTC] NOWPaymentsAPI SUCCESS: Прямая выплата создана успешно
[29-May-2025 14:51:00 UTC] NOWPaymentsAPI INFO: Попытка создания выплаты с автоконвертацией
[29-May-2025 14:51:00 UTC] NOWPaymentsAPI INFO: Цель - 0.1 usdttrc20 на адрес TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK
[29-May-2025 14:51:00 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 14:51:00 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":"0.1","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 14:51:00 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzAyNTQsImV4cCI6MTc0ODUzMDU1NH0.Gje-9xmfYbAI9sp6_zK-qS7wD9cNed11-ztVQ71OnZU","Content-Type: application\/json"]
[29-May-2025 14:51:01 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"usdttrc20":{"requested":0.1,"actualBalance":0}}}
[29-May-2025 14:51:01 UTC] NOWPaymentsAPI ERROR: Insufficient balance
[29-May-2025 14:51:01 UTC] NOWPaymentsAPI INFO: Прямая выплата не удалась, пробуем автоконвертацию
[29-May-2025 14:51:01 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 14:51:02 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 14:51:02 UTC] NOWPaymentsAPI INFO: Найдена опция конвертации: 9.3585168622477E-7 btc -> 0.1 usdttrc20
[29-May-2025 14:51:02 UTC] NOWPaymentsAPI INFO: Выбрана лучшая опция конвертации: 9.3585168622477E-7 btc
[29-May-2025 14:51:02 UTC] NOWPaymentsAPI ERROR: Адрес пользователя TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK несовместим с валютой btc
[29-May-2025 14:51:02 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 14:51:03 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"**********************************","currency":"btc","amount":"0.000005","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 14:51:03 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzAyNTQsImV4cCI6MTc0ODUzMDU1NH0.Gje-9xmfYbAI9sp6_zK-qS7wD9cNed11-ztVQ71OnZU","Content-Type: application\/json"]
[29-May-2025 14:51:04 UTC] NOWPaymentsAPI SUCCESS: Выплата создана успешно
[29-May-2025 14:51:04 UTC] NOWPaymentsAPI INFO: Ответ API: {"id":"5003096106","withdrawals":[{"is_request_payouts":false,"id":"5003725328","address":"**********************************","currency":"btc","amount":"0.000005","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php","batch_withdrawal_id":"5003096106","status":"CREATING","error":null,"extra_id":null,"hash":null,"payout_description":null,"unique_external_id":null,"created_at":"2025-05-29T14:51:01.651Z","requested_at":null,"updated_at":null,"update_history_log":null,"rejected_check_attempts":0,"fee":null,"fee_paid_by":null}]}
[29-May-2025 14:54:19 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 14:54:20 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=usd с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 14:54:21 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 14:54:21 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"**********************************","currency":"btc","amount":2.6e-6,"ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 14:54:21 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[29-May-2025 14:54:21 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[29-May-2025 14:54:21 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[29-May-2025 14:54:22 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzA0NTksImV4cCI6MTc0ODUzMDc1OX0.rDunGISocnT9IKMsw7m5B2gaMiS1DDpYJwrdcmkwTYY"}
[29-May-2025 14:54:22 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[29-May-2025 14:54:22 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzA0NTksImV4cCI6MTc0ODUzMDc1OX0.rDunGISocnT9IKMsw7m5B2gaMiS1DDpYJwrdcmkwTYY","Content-Type: application\/json"]
[29-May-2025 14:54:24 UTC] NOWPaymentsAPI SUCCESS: Выплата создана успешно
[29-May-2025 14:54:24 UTC] NOWPaymentsAPI INFO: Ответ API: {"id":"5003096115","withdrawals":[{"is_request_payouts":false,"id":"5003725337","address":"**********************************","currency":"btc","amount":"0.000002","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php","batch_withdrawal_id":"5003096115","status":"CREATING","error":null,"extra_id":null,"hash":null,"payout_description":null,"unique_external_id":null,"created_at":"2025-05-29T14:54:21.268Z","requested_at":null,"updated_at":null,"update_history_log":null,"rejected_check_attempts":0,"fee":null,"fee_paid_by":null}]}
[29-May-2025 14:54:24 UTC] NOWPaymentsAPI INFO: Попытка создания выплаты с автоконвертацией
[29-May-2025 14:54:24 UTC] NOWPaymentsAPI INFO: Цель - 0.1 usdttrc20 на адрес **********************************
[29-May-2025 14:54:24 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 14:54:24 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"**********************************","currency":"usdttrc20","amount":"0.1","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 14:54:24 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzA0NTksImV4cCI6MTc0ODUzMDc1OX0.rDunGISocnT9IKMsw7m5B2gaMiS1DDpYJwrdcmkwTYY","Content-Type: application\/json"]
[29-May-2025 14:54:26 UTC] NOWPaymentsAPI SUCCESS: Выплата создана успешно
[29-May-2025 14:54:26 UTC] NOWPaymentsAPI INFO: Ответ API: {"id":"5003096116","withdrawals":[{"is_request_payouts":false,"id":"5003725338","address":"**********************************","currency":"usdttrc20","amount":"0.1","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php","error":"Invalid payout address: USDTTRC20 **********************************","status":"REJECTED","batch_withdrawal_id":"5003096116","extra_id":null,"hash":null,"payout_description":null,"unique_external_id":null,"created_at":"2025-05-29T14:54:23.077Z","requested_at":null,"updated_at":null,"update_history_log":null,"rejected_check_attempts":0,"fee":null,"fee_paid_by":null}]}
[29-May-2025 14:54:26 UTC] NOWPaymentsAPI SUCCESS: Прямая выплата создана успешно
[29-May-2025 14:54:26 UTC] NOWPaymentsAPI INFO: Попытка создания выплаты с автоконвертацией
[29-May-2025 14:54:26 UTC] NOWPaymentsAPI INFO: Цель - 0.1 usdttrc20 на адрес TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK
[29-May-2025 14:54:26 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 14:54:26 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":"0.1","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 14:54:26 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzA0NTksImV4cCI6MTc0ODUzMDc1OX0.rDunGISocnT9IKMsw7m5B2gaMiS1DDpYJwrdcmkwTYY","Content-Type: application\/json"]
[29-May-2025 14:54:28 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"usdttrc20":{"requested":0.1,"actualBalance":0}}}
[29-May-2025 14:54:28 UTC] NOWPaymentsAPI ERROR: Insufficient balance
[29-May-2025 14:54:28 UTC] NOWPaymentsAPI INFO: Прямая выплата не удалась, пробуем автоконвертацию
[29-May-2025 14:54:28 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 14:54:28 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 14:54:29 UTC] NOWPaymentsAPI INFO: Найдена опция конвертации: 9.3553380479439E-7 btc -> 0.1 usdttrc20
[29-May-2025 14:54:29 UTC] NOWPaymentsAPI INFO: Выбрана лучшая опция конвертации: 9.3553380479439E-7 btc
[29-May-2025 14:54:29 UTC] NOWPaymentsAPI ERROR: Адрес пользователя TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK несовместим с валютой btc
[29-May-2025 14:56:50 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 14:56:51 UTC] NOWPaymentsAPI INFO: Попытка создания выплаты с автоконвертацией
[29-May-2025 14:56:51 UTC] NOWPaymentsAPI INFO: Цель - 0.05 usdttrc20 на адрес **********************************
[29-May-2025 14:56:51 UTC] NOWPaymentsAPI INFO: Адрес ********************************** несовместим с usdttrc20, переходим к автоконвертации
[29-May-2025 14:56:51 UTC] NOWPaymentsAPI INFO: Прямая выплата не удалась или адрес несовместим, пробуем автоконвертацию
[29-May-2025 14:56:51 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 14:56:52 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 14:56:53 UTC] NOWPaymentsAPI INFO: Найдена опция конвертации: 4.6607878595798E-7 btc -> 0.05 usdttrc20
[29-May-2025 14:56:53 UTC] NOWPaymentsAPI INFO: Выбрана лучшая опция конвертации: 4.6607878595798E-7 btc
[29-May-2025 14:56:53 UTC] NOWPaymentsAPI INFO: Адрес пользователя совместим с btc, создаем выплату
[29-May-2025 14:56:53 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 14:56:53 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"**********************************","currency":"btc","amount":4.660787859579783e-7,"ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 14:56:53 UTC] NOWPaymentsAPI INFO: Попытка получения JWT токена для email: <EMAIL>
[29-May-2025 14:56:53 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/auth без авторизации
[29-May-2025 14:56:53 UTC] NOWPaymentsAPI INFO: Данные запроса: {"email":"<EMAIL>","password":"Yjen10,er20"}
[29-May-2025 14:56:53 UTC] NOWPaymentsAPI SUCCESS: Ответ получен: {"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzA2MTEsImV4cCI6MTc0ODUzMDkxMX0.zO-75Gj3lWfKfoAjwKXyx1u30CajoZ_1_d_5jbV26d4"}
[29-May-2025 14:56:53 UTC] NOWPaymentsAPI SUCCESS: JWT токен получен успешно
[29-May-2025 14:56:53 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzA2MTEsImV4cCI6MTc0ODUzMDkxMX0.zO-75Gj3lWfKfoAjwKXyx1u30CajoZ_1_d_5jbV26d4","Content-Type: application\/json"]
[29-May-2025 14:56:56 UTC] NOWPaymentsAPI SUCCESS: Выплата создана успешно
[29-May-2025 14:56:56 UTC] NOWPaymentsAPI INFO: Ответ API: {"id":"5003096121","withdrawals":[{"is_request_payouts":false,"id":"5003725343","address":"**********************************","currency":"btc","amount":"0","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php","error":"Withdrawal amount must be greater than 0","status":"REJECTED","batch_withdrawal_id":"5003096121","extra_id":null,"hash":null,"payout_description":null,"unique_external_id":null,"created_at":"2025-05-29T14:56:52.963Z","requested_at":null,"updated_at":null,"update_history_log":null,"rejected_check_attempts":0,"fee":null,"fee_paid_by":null}]}
[29-May-2025 14:56:56 UTC] NOWPaymentsAPI SUCCESS: Выплата с автоконвертацией создана успешно
[29-May-2025 14:56:56 UTC] NOWPaymentsAPI INFO: Попытка создания выплаты с автоконвертацией
[29-May-2025 14:56:56 UTC] NOWPaymentsAPI INFO: Цель - 0.05 usdttrc20 на адрес TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK
[29-May-2025 14:56:56 UTC] NOWPaymentsAPI INFO: Адрес совместим с usdttrc20, пробуем прямую выплату
[29-May-2025 14:56:56 UTC] NOWPaymentsAPI INFO: Создание выплаты через /payout endpoint
[29-May-2025 14:56:56 UTC] NOWPaymentsAPI INFO: Данные запроса: {"withdrawals":[{"address":"TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK","currency":"usdttrc20","amount":"0.05","ipn_callback_url":"https:\/\/app.uniqpaid.com\/test2\/api\/withdrawal_callback.php"}]}
[29-May-2025 14:56:56 UTC] NOWPaymentsAPI INFO: Отправка запроса POST https://api.nowpayments.io/v1/payout с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjUxNjU1NDM4MzciLCJpYXQiOjE3NDg1MzA2MTEsImV4cCI6MTc0ODUzMDkxMX0.zO-75Gj3lWfKfoAjwKXyx1u30CajoZ_1_d_5jbV26d4","Content-Type: application\/json"]
[29-May-2025 14:56:57 UTC] NOWPaymentsAPI HTTP INFO: Code 400, Response: {"statusCode":400,"code":"BAD_CREATE_WITHDRAWAL_REQUEST","message":"Insufficient balance","current_values":{"usdttrc20":{"requested":0.05,"actualBalance":0}}}
[29-May-2025 14:56:57 UTC] NOWPaymentsAPI ERROR: Insufficient balance
[29-May-2025 14:56:57 UTC] NOWPaymentsAPI INFO: Прямая выплата не удалась или адрес несовместим, пробуем автоконвертацию
[29-May-2025 14:56:57 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/balance с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 14:56:58 UTC] NOWPaymentsAPI INFO: Отправка запроса GET https://api.nowpayments.io/v1/estimate?amount=2.6E-5&currency_from=btc&currency_to=usdttrc20 с заголовками: ["x-api-key: 18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7","Content-Type: application\/json"]
[29-May-2025 14:56:59 UTC] NOWPaymentsAPI INFO: Найдена опция конвертации: 4.6569160397443E-7 btc -> 0.05 usdttrc20
[29-May-2025 14:56:59 UTC] NOWPaymentsAPI INFO: Выбрана лучшая опция конвертации: 4.6569160397443E-7 btc
[29-May-2025 14:56:59 UTC] NOWPaymentsAPI ERROR: Адрес пользователя TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK несовместим с валютой btc
