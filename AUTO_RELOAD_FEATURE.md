# 🔄 Автоматическое обновление рекламы после таймера

## 📋 Описание функции

После окончания 20-секундного таймера после просмотра рекламы приложение автоматически обновляет рекламные блоки для получения новой рекламы.

## ⚙️ Настройки

В файле `main.js` есть две константы для управления этой функцией:

```javascript
const AUTO_RELOAD_AFTER_COUNTDOWN = true; // Включить/выключить автообновление
const USE_SOFT_REFRESH = false; // Тип обновления: мягкое или полное
```

## 🔧 Режимы работы

### 1. Полное обновление (по умолчанию)
```javascript
const AUTO_RELOAD_AFTER_COUNTDOWN = true;
const USE_SOFT_REFRESH = false;
```

**Что происходит:**
- После таймера выполняется `window.location.reload()`
- Страница полностью перезагружается
- Восстанавливается последняя активная вкладка
- Все рекламные блоки обновляются

**Плюсы:**
- ✅ Гарантированное обновление всех рекламных блоков
- ✅ Полная очистка памяти
- ✅ Максимальная вероятность получения новой рекламы

**Минусы:**
- ⚠️ Небольшая задержка на перезагрузку
- ⚠️ Потеря состояния форм (если есть)

### 2. Мягкое обновление
```javascript
const AUTO_RELOAD_AFTER_COUNTDOWN = true;
const USE_SOFT_REFRESH = true;
```

**Что происходит:**
- Переинициализация RichAds SDK
- Страница не перезагружается
- Состояние приложения сохраняется

**Плюсы:**
- ✅ Быстрое обновление без перезагрузки
- ✅ Сохранение состояния приложения
- ✅ Плавный пользовательский опыт

**Минусы:**
- ⚠️ Может не всегда обновить все блоки
- ⚠️ Зависит от реализации SDK

### 3. Отключено
```javascript
const AUTO_RELOAD_AFTER_COUNTDOWN = false;
```

**Что происходит:**
- Никакого автоматического обновления
- Показывается сообщение "Можете смотреть рекламу снова!"

## 🔄 Восстановление страницы

При полном reload приложение автоматически:

1. **Сохраняет** текущую активную вкладку в `localStorage`
2. **Перезагружает** страницу
3. **Восстанавливает** последнюю активную вкладку

Поддерживаемые вкладки:
- `main-content` - Главная
- `earn-section` - Заработок  
- `friends-section` - Друзья

## 📱 Пользовательский опыт

### С автообновлением:
1. Пользователь смотрит рекламу
2. Получает награду
3. Видит таймер 20 секунд
4. После таймера: "Обновляем рекламные блоки..."
5. Страница обновляется
6. Пользователь остается на той же вкладке
7. Доступна новая реклама

### Без автообновления:
1. Пользователь смотрит рекламу
2. Получает награду
3. Видит таймер 20 секунд
4. После таймера: "Можете смотреть рекламу снова!"
5. Может быть доступна та же реклама

## 🛠️ Техническая реализация

### Функция `startCountdown()`
- Добавлена логика автообновления после таймера
- Проверка настроек `AUTO_RELOAD_AFTER_COUNTDOWN` и `USE_SOFT_REFRESH`
- Сохранение активной страницы перед reload

### Функция `refreshAdBlocks()`
- Мягкое обновление через переинициализацию SDK
- Fallback на полную переинициализацию при ошибках

### Функция `initializeApp()`
- Восстановление активной страницы из `localStorage`
- Автоматическое переключение на сохраненную вкладку

## 🔍 Логирование

Все действия логируются в консоль:
```
[Countdown] Таймер завершен, выполняем reload страницы для обновления рекламы
[Page Restore] Восстанавливаем последнюю активную страницу: earn-section
[Ad Refresh] Попытка обновления рекламных блоков...
```

## 🎯 Рекомендации

### Для максимального количества рекламы:
```javascript
const AUTO_RELOAD_AFTER_COUNTDOWN = true;
const USE_SOFT_REFRESH = false;
```

### Для плавного UX:
```javascript
const AUTO_RELOAD_AFTER_COUNTDOWN = true;
const USE_SOFT_REFRESH = true;
```

### Для отладки:
```javascript
const AUTO_RELOAD_AFTER_COUNTDOWN = false;
```

## 🚀 Результат

Эта функция решает проблему нехватки рекламы, автоматически обновляя рекламные блоки после каждого просмотра, что увеличивает количество доступной рекламы для пользователей.
