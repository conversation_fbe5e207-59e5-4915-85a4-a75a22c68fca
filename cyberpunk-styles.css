/* 🔥 CYBERPUNK DESIGN SYSTEM 🔥 */

/* === CYBERPUNK COLOR PALETTE === */
:root {
  /* Neon Colors */
  --cyber-neon-blue: #00ffff;
  --cyber-neon-pink: #ff0080;
  --cyber-neon-green: #00ff41;
  --cyber-neon-purple: #8a2be2;
  --cyber-neon-orange: #ff6600;
  --cyber-neon-yellow: #ffff00;
  
  /* Dark Backgrounds */
  --cyber-bg-primary: #0a0a0a;
  --cyber-bg-secondary: #1a1a2e;
  --cyber-bg-tertiary: #16213e;
  --cyber-bg-card: #0f0f23;
  
  /* Gradients */
  --cyber-gradient-main: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
  --cyber-gradient-neon: linear-gradient(45deg, #00ffff, #ff0080, #00ff41);
  --cyber-gradient-card: linear-gradient(145deg, #0f0f23 0%, #1a1a2e 100%);
  
  /* Text Colors */
  --cyber-text-primary: #ffffff;
  --cyber-text-secondary: #b0b0b0;
  --cyber-text-accent: #00ffff;
  --cyber-text-warning: #ff6600;
  --cyber-text-success: #00ff41;
  --cyber-text-error: #ff0080;
  
  /* Shadows & Glows */
  --cyber-glow-blue: 0 0 20px rgba(0, 255, 255, 0.5);
  --cyber-glow-pink: 0 0 20px rgba(255, 0, 128, 0.5);
  --cyber-glow-green: 0 0 20px rgba(0, 255, 65, 0.5);
  --cyber-glow-purple: 0 0 20px rgba(138, 43, 226, 0.5);
  
  /* Borders */
  --cyber-border-neon: 2px solid var(--cyber-neon-blue);
  --cyber-border-glow: 1px solid rgba(0, 255, 255, 0.3);
}

/* === CYBERPUNK FONTS === */
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap');

/* === GLOBAL CYBERPUNK STYLES === */
* {
  box-sizing: border-box;
}

body {
  font-family: 'Rajdhani', sans-serif;
  background: var(--cyber-bg-primary);
  background-image: 
    radial-gradient(circle at 20% 80%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 0, 128, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(0, 255, 65, 0.05) 0%, transparent 50%);
  color: var(--cyber-text-primary);
  margin: 0;
  padding: 0;
  min-height: 100vh;
  overflow-x: hidden;
}

/* === CYBERPUNK CONTAINER === */
.app-container {
  background: var(--cyber-gradient-main);
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

.app-container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    linear-gradient(90deg, transparent 0%, rgba(0, 255, 255, 0.03) 50%, transparent 100%),
    linear-gradient(0deg, transparent 0%, rgba(255, 0, 128, 0.03) 50%, transparent 100%);
  pointer-events: none;
  z-index: 1;
}

/* === CYBERPUNK HEADERS === */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Orbitron', monospace;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 2px;
  margin: 0 0 20px 0;
  position: relative;
}

h1 {
  font-size: 2.5rem;
  background: var(--cyber-gradient-neon);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: var(--cyber-glow-blue);
}

h2 {
  font-size: 2rem;
  color: var(--cyber-neon-blue);
  text-shadow: var(--cyber-glow-blue);
}

h3 {
  font-size: 1.5rem;
  color: var(--cyber-neon-pink);
  text-shadow: var(--cyber-glow-pink);
}

/* === CYBERPUNK CARDS === */
.earn-block, .friends-block {
  background: var(--cyber-gradient-card);
  border: var(--cyber-border-glow);
  border-radius: 15px;
  padding: 25px;
  margin: 20px 0;
  position: relative;
  overflow: hidden;
  box-shadow: 
    var(--cyber-glow-blue),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  z-index: 2;
}

.earn-block::before, .friends-block::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--cyber-gradient-neon);
  opacity: 0.8;
}

.earn-block::after, .friends-block::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: 
    radial-gradient(circle, rgba(0, 255, 255, 0.05) 0%, transparent 70%);
  animation: cyber-pulse 4s ease-in-out infinite;
  pointer-events: none;
}

@keyframes cyber-pulse {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 0.1; transform: scale(1.1); }
}

/* === CYBERPUNK BUTTONS === */
.action-button, .nav-button, .currency-tab {
  font-family: 'Orbitron', monospace;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  background: var(--cyber-gradient-card);
  border: var(--cyber-border-glow);
  border-radius: 10px;
  padding: 15px 25px;
  color: var(--cyber-text-primary);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: var(--cyber-glow-blue);
}

.action-button:hover, .nav-button:hover, .currency-tab:hover {
  border-color: var(--cyber-neon-blue);
  box-shadow: var(--cyber-glow-blue);
  transform: translateY(-2px);
}

.action-button:active, .nav-button:active, .currency-tab:active {
  transform: translateY(0);
}

.action-button.primary-action {
  background: linear-gradient(135deg, var(--cyber-neon-blue), var(--cyber-neon-purple));
  border-color: var(--cyber-neon-blue);
  color: var(--cyber-bg-primary);
  font-weight: 700;
}

.action-button.secondary-action {
  background: linear-gradient(135deg, var(--cyber-neon-pink), var(--cyber-neon-orange));
  border-color: var(--cyber-neon-pink);
  color: var(--cyber-bg-primary);
}

/* === CYBERPUNK INPUTS === */
input, select, textarea {
  font-family: 'Rajdhani', sans-serif;
  background: rgba(0, 0, 0, 0.7);
  border: var(--cyber-border-glow);
  border-radius: 8px;
  padding: 15px;
  color: var(--cyber-text-primary);
  font-size: 16px;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
}

input:focus, select:focus, textarea:focus {
  outline: none;
  border-color: var(--cyber-neon-blue);
  box-shadow: var(--cyber-glow-blue);
  background: rgba(0, 0, 0, 0.9);
}

input::placeholder {
  color: var(--cyber-text-secondary);
  font-style: italic;
}

/* === CYBERPUNK NAVIGATION === */
.app-nav {
  background: var(--cyber-gradient-card);
  border-top: var(--cyber-border-glow);
  backdrop-filter: blur(20px);
  box-shadow: 
    0 -5px 20px rgba(0, 0, 0, 0.5),
    var(--cyber-glow-blue);
  position: relative;
}

.app-nav::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--cyber-gradient-neon);
  opacity: 0.8;
}

.nav-button {
  background: transparent;
  border: none;
  padding: 15px;
  border-radius: 0;
  box-shadow: none;
}

.nav-button.active {
  background: linear-gradient(135deg, rgba(0, 255, 255, 0.2), rgba(255, 0, 128, 0.2));
  box-shadow: inset var(--cyber-glow-blue);
}

.nav-icon {
  width: 24px;
  height: 24px;
  fill: var(--cyber-neon-blue);
  filter: drop-shadow(var(--cyber-glow-blue));
}

.nav-text {
  color: var(--cyber-text-accent);
  font-weight: 600;
  text-shadow: var(--cyber-glow-blue);
}

/* === CYBERPUNK ANIMATIONS === */
@keyframes cyber-flicker {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

@keyframes cyber-scan {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100vw); }
}

@keyframes cyber-glow-pulse {
  0%, 100% { 
    box-shadow: var(--cyber-glow-blue);
  }
  33% { 
    box-shadow: var(--cyber-glow-pink);
  }
  66% { 
    box-shadow: var(--cyber-glow-green);
  }
}

/* === CYBERPUNK EFFECTS === */
.cyber-scan-line {
  position: fixed;
  top: 0;
  left: -2px;
  width: 2px;
  height: 100vh;
  background: linear-gradient(to bottom, 
    transparent 0%, 
    var(--cyber-neon-blue) 50%, 
    transparent 100%);
  animation: cyber-scan 8s linear infinite;
  z-index: 1000;
  pointer-events: none;
}

.cyber-glitch {
  animation: cyber-flicker 0.15s infinite linear alternate-reverse;
}

/* === CYBERPUNK CALCULATOR === */
.calculator-header {
  background: linear-gradient(135deg, rgba(0, 255, 255, 0.1), rgba(255, 0, 128, 0.1));
  border: var(--cyber-border-glow);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 25px;
  position: relative;
  overflow: hidden;
}

.calculator-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--cyber-gradient-neon);
  animation: cyber-scan 3s linear infinite;
}

.calculator-subtitle {
  color: var(--cyber-text-accent);
  font-family: 'Orbitron', monospace;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin: 0;
}

.balance-amount {
  color: var(--cyber-neon-green);
  font-family: 'Orbitron', monospace;
  font-weight: 700;
  text-shadow: var(--cyber-glow-green);
  font-size: 1.2rem;
}

/* === CYBERPUNK INPUT SECTION === */
.amount-input-section {
  background: var(--cyber-gradient-card);
  border: var(--cyber-border-glow);
  border-radius: 15px;
  padding: 25px;
  margin-bottom: 25px;
  position: relative;
  box-shadow: var(--cyber-glow-blue);
}

.input-group {
  position: relative;
  margin-bottom: 15px;
}

.input-group input {
  width: 100%;
  padding: 18px 80px 18px 18px;
  background: rgba(0, 0, 0, 0.8);
  border: 2px solid rgba(0, 255, 255, 0.3);
  border-radius: 10px;
  color: var(--cyber-text-primary);
  font-family: 'Orbitron', monospace;
  font-size: 1.2rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.input-group input:focus {
  border-color: var(--cyber-neon-blue);
  box-shadow: var(--cyber-glow-blue);
  background: rgba(0, 0, 0, 0.95);
}

.input-suffix {
  position: absolute;
  right: 18px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--cyber-neon-blue);
  font-family: 'Orbitron', monospace;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  pointer-events: none;
}

.amount-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 15px;
}

#dollar-equivalent {
  color: var(--cyber-neon-yellow);
  font-family: 'Orbitron', monospace;
  font-weight: 700;
  font-size: 1.1rem;
  text-shadow: 0 0 10px rgba(255, 255, 0, 0.5);
}

.balance-status {
  padding: 8px 15px;
  border-radius: 20px;
  font-family: 'Orbitron', monospace;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.balance-status.sufficient {
  background: rgba(0, 255, 65, 0.2);
  color: var(--cyber-neon-green);
  border: 1px solid var(--cyber-neon-green);
  box-shadow: var(--cyber-glow-green);
}

.balance-status.insufficient {
  background: rgba(255, 0, 128, 0.2);
  color: var(--cyber-neon-pink);
  border: 1px solid var(--cyber-neon-pink);
  box-shadow: var(--cyber-glow-pink);
}

.balance-status.neutral {
  background: rgba(0, 255, 255, 0.2);
  color: var(--cyber-neon-blue);
  border: 1px solid var(--cyber-neon-blue);
  box-shadow: var(--cyber-glow-blue);
}

/* === CYBERPUNK CURRENCY TABS === */
.currency-tabs-container {
  margin-top: 25px;
}

.currency-tabs-header {
  display: flex;
  background: rgba(0, 0, 0, 0.7);
  border: var(--cyber-border-glow);
  border-radius: 15px;
  padding: 5px;
  margin-bottom: 25px;
  gap: 3px;
  backdrop-filter: blur(10px);
}

.currency-tab {
  flex: 1;
  min-width: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  padding: 15px 10px;
  background: transparent;
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: 10px;
  color: var(--cyber-text-secondary);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.currency-tab::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.currency-tab:hover::before {
  left: 100%;
}

.currency-tab:hover {
  border-color: var(--cyber-neon-blue);
  color: var(--cyber-text-primary);
  box-shadow: var(--cyber-glow-blue);
}

.currency-tab.active {
  background: linear-gradient(135deg, rgba(0, 255, 255, 0.2), rgba(255, 0, 128, 0.2));
  border-color: var(--cyber-neon-blue);
  color: var(--cyber-text-primary);
  box-shadow: var(--cyber-glow-blue);
}

.currency-tab.available {
  border-color: var(--cyber-neon-green);
  box-shadow: 0 0 10px rgba(0, 255, 65, 0.3);
}

.currency-tab.warning {
  border-color: var(--cyber-neon-orange);
  box-shadow: 0 0 10px rgba(255, 102, 0, 0.3);
}

.currency-tab.insufficient {
  border-color: var(--cyber-neon-pink);
  box-shadow: 0 0 10px rgba(255, 0, 128, 0.3);
}

.tab-icon {
  font-size: 20px;
  filter: drop-shadow(0 0 5px currentColor);
}

.tab-name {
  font-family: 'Orbitron', monospace;
  font-weight: 600;
  font-size: 13px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.tab-symbol {
  font-size: 10px;
  opacity: 0.8;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* === RESPONSIVE CYBERPUNK === */
@media (max-width: 768px) {
  h1 { font-size: 2rem; }
  h2 { font-size: 1.5rem; }
  h3 { font-size: 1.2rem; }

  .earn-block, .friends-block {
    padding: 20px;
    margin: 15px 0;
  }

  .action-button {
    padding: 12px 20px;
    font-size: 14px;
  }

  .currency-tabs-header {
    padding: 3px;
    gap: 2px;
  }

  .currency-tab {
    min-width: 90px;
    padding: 12px 8px;
  }

  .tab-name {
    font-size: 11px;
  }

  .input-group input {
    font-size: 1rem;
    padding: 15px 70px 15px 15px;
  }
}

/* === CYBERPUNK CURRENCY INFO CARD === */
.currency-info-card {
  background: var(--cyber-gradient-card);
  border: var(--cyber-border-glow);
  border-radius: 20px;
  padding: 30px;
  position: relative;
  overflow: hidden;
  box-shadow: var(--cyber-glow-blue);
  backdrop-filter: blur(15px);
}

.currency-info-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--cyber-gradient-neon);
  opacity: 0.9;
}

.currency-info-card::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(0, 255, 255, 0.03) 0%, transparent 70%);
  animation: cyber-pulse 6s ease-in-out infinite;
  pointer-events: none;
}

.currency-title {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.currency-icon {
  font-size: 28px;
  filter: drop-shadow(var(--cyber-glow-blue));
}

.currency-full-name {
  font-family: 'Orbitron', monospace;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--cyber-text-primary);
  text-transform: uppercase;
  letter-spacing: 2px;
  text-shadow: var(--cyber-glow-blue);
}

.currency-badge {
  padding: 8px 16px;
  border-radius: 25px;
  font-family: 'Orbitron', monospace;
  font-size: 11px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
  position: relative;
  overflow: hidden;
}

.currency-badge.status-best {
  background: linear-gradient(135deg, var(--cyber-neon-blue), var(--cyber-neon-purple));
  color: var(--cyber-bg-primary);
  box-shadow: var(--cyber-glow-blue);
  animation: cyber-glow-pulse 3s ease-in-out infinite;
}

.currency-badge.status-good {
  background: rgba(0, 255, 65, 0.2);
  color: var(--cyber-neon-green);
  border: 1px solid var(--cyber-neon-green);
  box-shadow: var(--cyber-glow-green);
}

.currency-badge.status-expensive {
  background: rgba(255, 0, 128, 0.2);
  color: var(--cyber-neon-pink);
  border: 1px solid var(--cyber-neon-pink);
  box-shadow: var(--cyber-glow-pink);
}

.currency-requirements {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 25px;
}

.requirement-item {
  background: rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: 10px;
  padding: 15px;
  position: relative;
}

.requirement-label {
  font-family: 'Orbitron', monospace;
  font-size: 11px;
  color: var(--cyber-text-secondary);
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 8px;
  display: block;
}

.requirement-value {
  font-family: 'Orbitron', monospace;
  font-size: 14px;
  font-weight: 600;
  color: var(--cyber-text-primary);
}

.fee-amount {
  color: var(--cyber-neon-orange) !important;
  text-shadow: 0 0 10px rgba(255, 102, 0, 0.5);
}

/* === CYBERPUNK CALCULATION RESULTS === */
.calculation-results {
  background: rgba(0, 0, 0, 0.7);
  border: var(--cyber-border-glow);
  border-radius: 15px;
  padding: 25px;
  margin-bottom: 25px;
  position: relative;
  backdrop-filter: blur(10px);
}

.calculation-results::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--cyber-gradient-neon);
  opacity: 0.6;
}

.calculation-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid rgba(0, 255, 255, 0.1);
  position: relative;
}

.calculation-row:last-child {
  border-bottom: none;
}

.total-row {
  border-top: 2px solid var(--cyber-neon-blue);
  margin-top: 15px;
  padding-top: 15px;
  border-bottom: none;
  background: rgba(0, 255, 255, 0.05);
  border-radius: 8px;
  padding: 15px;
  margin: 15px -10px 0 -10px;
}

.calc-label {
  font-family: 'Rajdhani', sans-serif;
  font-size: 14px;
  color: var(--cyber-text-secondary);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.calc-value {
  font-family: 'Orbitron', monospace;
  font-size: 16px;
  font-weight: 600;
  color: var(--cyber-text-primary);
}

.fee-value {
  color: var(--cyber-neon-orange);
  text-shadow: 0 0 10px rgba(255, 102, 0, 0.5);
}

.total-value {
  color: var(--cyber-neon-green);
  font-size: 20px;
  font-weight: 700;
  text-shadow: var(--cyber-glow-green);
}

.efficiency-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid rgba(0, 255, 255, 0.2);
}

.efficiency-label {
  font-family: 'Orbitron', monospace;
  font-size: 12px;
  color: var(--cyber-text-secondary);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.efficiency-value {
  font-family: 'Orbitron', monospace;
  font-size: 14px;
  font-weight: 600;
  color: var(--cyber-neon-blue);
  text-shadow: var(--cyber-glow-blue);
}

/* === CYBERPUNK ACTION STATUS === */
.action-status-card {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  border-radius: 15px;
  font-family: 'Orbitron', monospace;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.action-status-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.8s ease;
}

.action-status-card:hover::before {
  left: 100%;
}

.action-status-card.available {
  background: linear-gradient(135deg, rgba(0, 255, 65, 0.2), rgba(0, 255, 65, 0.1));
  border: 2px solid var(--cyber-neon-green);
  color: var(--cyber-neon-green);
  box-shadow: var(--cyber-glow-green);
}

.action-status-card.insufficient-funds {
  background: linear-gradient(135deg, rgba(255, 0, 128, 0.2), rgba(255, 0, 128, 0.1));
  border: 2px solid var(--cyber-neon-pink);
  color: var(--cyber-neon-pink);
  box-shadow: var(--cyber-glow-pink);
}

.action-status-card.insufficient-minimum {
  background: linear-gradient(135deg, rgba(255, 102, 0, 0.2), rgba(255, 102, 0, 0.1));
  border: 2px solid var(--cyber-neon-orange);
  color: var(--cyber-neon-orange);
  box-shadow: 0 0 20px rgba(255, 102, 0, 0.5);
}

.action-status-card.loss {
  background: linear-gradient(135deg, rgba(138, 43, 226, 0.2), rgba(138, 43, 226, 0.1));
  border: 2px solid var(--cyber-neon-purple);
  color: var(--cyber-neon-purple);
  box-shadow: var(--cyber-glow-purple);
}

.action-status-card.neutral {
  background: linear-gradient(135deg, rgba(0, 255, 255, 0.2), rgba(0, 255, 255, 0.1));
  border: 2px solid var(--cyber-neon-blue);
  color: var(--cyber-neon-blue);
  box-shadow: var(--cyber-glow-blue);
}

.status-icon {
  font-size: 24px;
  filter: drop-shadow(0 0 10px currentColor);
}

.status-text {
  font-size: 14px;
  line-height: 1.4;
}
