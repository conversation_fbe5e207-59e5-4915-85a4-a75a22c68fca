<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no, user-scalable=no, viewport-fit=cover">
    <title>Applanza App</title> <!-- Обновил название -->
    <link rel="stylesheet" href="styles.css">
    <!-- Скрипты подключаются в конце body -->
</head>
<body>

    <!-- Контейнер приложения -->
    <div class="app-container">

        <!-- Шапка -->
        <header class="app-header">
            <div class="user-info">
                <!-- Аватар пока плейсхолдер, можно заменить на img или div с фоном -->
                <div class="user-avatar">
                    <img src="./images/user.svg" class="user-avatar-icon" alt="user">
                </div>
                <div class="user-name" id="user-name">Загрузка...</div>
            </div>
            <div class="balance-info clickable-balance" id="header-balance-info" title="Перейти к выводу средств">
                <!-- Иконка баланса из спрайта -->
                <svg class="balance-icon"></svg> <!-- Или #icon-dollar, #icon-money -->
                <span class="balance-amount" id="balance-amount">0</span>
                <span class="balance-currency">монет</span> <!-- Оставим "монет" или можно ₽ -->
            </div>
        </header>

        <!-- Основной контент (Задания) -->
        <main class="app-section active-section" id="main-content"> <!-- Добавлен класс active-section -->
            <div id="status-message" class="status-message">Ожидание инициализации...</div>
            <h2 data-section="tasks">Задания</h2>
            <button id="openLinkButton" class="action-button purple-button">
                Открыть ссылку
            </button>
            <button id="watchVideoButton" class="action-button blue-button">
                Смотреть видео
            </button>
            <button id="openAdButton" class="action-button orange-button">
                Открыть рекламу
            </button>
             <!-- <button id="paid-survey-button" class="action-button secondary-action" disabled>
                 <svg class="button-icon"><use href="images/sprite.svg#icon-money"></use></svg>
                 Пройти опрос
            </button> -->
        </main>

        <!-- Секция "Заработок" (Вывод средств - заглушка) -->
        <section class="app-section earn-section page-hidden" id="earn-section"> <!-- Добавлен page-hidden -->
            <h2>Вывод средств</h2>
            <div class="earn-block">
                <h3>Ваш баланс</h3>
                <div class="current-balance-display">
                    <svg class="balance-icon"><use href="images/sprite.svg#icon-ruble"></use></svg>
                    <span class="balance-amount" id="earn-balance-amount">0</span>
                    <span class="balance-currency">монет</span>
                </div>
                <p class="hint">Доступно для вывода: <span id="available-withdrawal">0</span> монет.</p>
            </div>
             <div class="earn-block">
                <h3>Заявка на вывод</h3>
                 <p class="hint">Для доступа к выводу необходимо иметь минимум 100 монет на балансе. При достижении этого порога можно вывести любое количество (в пределах баланса).</p>
                 <div class="withdrawal-form">
                     <label for="withdrawal-amount">
                         Сумма для вывода:
                         <div class="help-tooltip">
                             <span class="help-icon">?</span>
                             <span class="tooltip-text">Ваш заработок автоматически будет конвертирован в выбранную криптовалюту по текущему курсу на Binance. 1 монета = $0.001</span>
                         </div>
                     </label>
                     <input type="number" id="withdrawal-amount" placeholder="Введите сумму" min="0" step="1">

                     <label for="crypto-currency">Выберите криптовалюту:</label>
                     <select id="crypto-currency" class="crypto-select">
                         <option value="usdttrc20">USDT (TRC20) - мин. 8.58 (комиссия ~12%)</option>
                         <option value="xrp">Ripple (XRP) - мин. 1.0 (комиссия 2%) ⭐ ВЫГОДНО</option>
                         <option value="trx">TRON (TRX) - мин. 1.0 (комиссия 10%)</option>
                         <option value="dot">Polkadot (DOT) - мин. 0.1 (комиссия 10%)</option>
                         <option value="ada">Cardano (ADA) - мин. 1.0 (комиссия 17%)</option>
                         <option value="ltc">Litecoin (LTC) - мин. 0.001 (комиссия 100%) ⚠️</option>
                         <option value="bch">Bitcoin Cash (BCH) - мин. 0.001 (комиссия 100%) ⚠️</option>
                         <option value="eth">Ethereum (ETH) - мин. 0.001 (комиссия 500%) ❌</option>
                         <option value="btc">Bitcoin (BTC) - мин. 0.000005 (комиссия 10000%) ❌</option>
                     </select>
                     <p class="hint" id="min-amount-info">Минимальная сумма для USDT (TRC20): 8.58, комиссия ~12%</p>
                     <p class="hint" id="fee-warning" style="color: #ff6b6b; font-weight: bold;">⚠️ Комиссия списывается с суммы получателя!</p>

                     <label for="crypto-amount">Сумма к получению в криптовалюте:</label>
                     <input type="text" id="crypto-amount" class="crypto-amount-field" placeholder="Будет рассчитано автоматически" readonly>

                     <label for="withdrawal-address">Адрес кошелька:</label>
                     <input type="text" id="withdrawal-address" placeholder="Введите адрес TRC20-кошелька USDT (например: TXYZ...)">

                     <button id="request-withdrawal-button" class="action-button primary-action" disabled>
                         Запросить вывод
                     </button>
                 </div>
                 <p class="hint error-message" id="withdrawal-error" style="display: none;"></p>
                 <p class="hint"><strong>Важно:</strong> Убедитесь, что адрес кошелька указан корректно. Средства будут отправлены на указанный адрес и не могут быть возвращены в случае ошибки.</p>
            </div>
            <div class="earn-block">
                <h3>История выплат</h3>
                <div id="withdrawal-history" class="withdrawal-history">
                    <div class="placeholder-list">Загрузка истории выплат...</div>
                </div>
            </div>
        </section>

        <!-- Секция "Друзья" -->
        <section class="app-section friends-section page-hidden" id="friends-section"> <!-- Добавлен page-hidden -->
            <h2>Друзья и Приглашения</h2>
            <div class="friends-block">
                <h3>Поделиться приложением</h3>
                <p>Расскажи друзьям об этом приложении!</p>
                <button id="share-app-button" class="action-button secondary-action">
                    Поделиться
                </button>
            </div>
            <div class="friends-block">
                <h3>Пригласить друга (Ваша ссылка)</h3>
                <p>Поделитесь ссылкой. Вы будете получать 10% от заработка друзей, пришедших по ней!</p>
                <div class="referral-link-area">
                    <input type="text" id="referral-link-input" value="Генерация ссылки..." readonly>
                    <button id="copy-referral-button" class="copy-button" title="Копировать" disabled>
                        <svg class="button-icon small-icon"><use href="images/sprite.svg#icon-link"></use></svg> <!-- Маленькая иконка копирования -->
                    </button>
                </div>
            </div>
            <div class="friends-block">
                <h3>Статистика рефералов</h3>
                <div class="referral-stats">
                    <div class="stat-item">
                        <div class="stat-label">Всего рефералов:</div>
                        <div class="stat-value" id="referrals-count">0</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">Заработано на рефералах:</div>
                        <div class="stat-value" id="referral-earnings">0</div>
                    </div>
                </div>
                <div id="referrals-list" class="referrals-list">
                    <p class="hint">У вас пока нет рефералов. Пригласите друзей!</p>
                </div>
                <button id="refresh-stats-button" class="action-button secondary-action">
                    Обновить статистику
                </button>
            </div>
            <div class="friends-block">
                <h3>Подписки</h3>
                <div id="subscriptions-list" class="referrals-list">
                    <p class="hint">Загрузка информации...</p>
                </div>
            </div>
        </section>

        <!-- Нижняя навигация (3 кнопки с SVG) -->
        <nav class="app-nav">
            <button class="nav-button active" id="nav-home">
                <svg class="nav-icon"><use href="images/sprite.svg#icon-home"></use></svg>
                <span class="nav-text" data-section="tasks">Главная</span>
            </button>
            <button class="nav-button" id="nav-earn">
                <svg class="nav-icon"><use href="images/sprite.svg#icon-dollar"></use></svg> <!-- Или #icon-ruble -->
                <span class="nav-text" data-section="earnings">Заработок</span>
            </button>
            <button class="nav-button" id="nav-friends">
                <svg class="nav-icon"><use href="images/sprite.svg#icon-friends"></use></svg>
                <span class="nav-text" data-section="referrals">Друзья</span>
            </button>
        </nav>

    </div> <!-- /app-container -->

    <!-- Подключение скриптов в конце -->
    <script src="https://telegram.org/js/telegram-web-app.js"></script>
    <script src="https://richinfo.co/richpartners/telegram/js/tg-ob.js"></script>
    <script src="js/localization.js"></script>
    <script src="main.js"></script>

</body>
</html>