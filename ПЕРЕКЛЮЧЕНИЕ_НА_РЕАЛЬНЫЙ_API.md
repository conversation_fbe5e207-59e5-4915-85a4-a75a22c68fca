# Система вывода средств через NOWPayments API

## Текущее состояние
Система настроена и готова к работе с **реальным NOWPayments API**.

## Настройки API
В файле `api/config.php` настроены реальные ключи:
```php
define('NOWPAYMENTS_API_KEY', '18A6Q9R-6DN4F1C-QJ217G3-P9J2WY7');
define('NOWPAYMENTS_IPN_SECRET', '+dtLfBgWRcW4ybhampqglG39/zxiGgwX');
define('NOWPAYMENTS_API_URL', 'https://api.nowpayments.io/v1');
```

## Что уже настроено

### 1. API интеграция
- ✅ Класс `NOWPaymentsAPI.php` для работы с API
- ✅ Функция создания выплат через `createWithdrawalRequest()`
- ✅ Обработка callback уведомлений в `withdrawal_callback.php`
- ✅ Проверка подписи IPN для безопасности

### 2. Логика вывода средств
- ✅ Минимальный баланс для доступа: 100 монет
- ✅ Минимальная сумма вывода: НЕТ (можно вывести любое количество в пределах баланса)
- ✅ Конвертация: 1 монета = 0.01 USD
- ✅ Поддержка криптовалют: USDT (TRC20), BTC, ETH, TRX, BNB

### 3. Безопасность
- ✅ Проверка подписи Telegram initData
- ✅ Валидация адресов кошельков
- ✅ Защита от подозрительной активности
- ✅ Проверка подписи IPN от NOWPayments

### 4. Настройки для продакшена
- ✅ Логирование ошибок включено
- ✅ Отображение ошибок отключено
- ✅ Все файлы готовы к загрузке на сервер

## Текущие настройки системы
- **Минимальный баланс для доступа к выводу:** 100 монет
- **Минимальная сумма вывода:** НЕТ (можно вывести любое количество в пределах баланса)
- **Курс конвертации:** 1 монета = 0.01 USD

## Безопасность
Система включает следующие проверки:
- Проверка минимального баланса для доступа к выводу
- Проверка достаточности средств
- Проверка корректности адреса кошелька
- Защита от подозрительной активности
- Логирование всех операций

## Логи
Все операции логируются в `api/error.log` для отладки и мониторинга.
