# 🌍 Исправление определения языка

## 🐛 Проблема

Пользователи с английской локалью в Telegram (`language_code: "en"`) видели русский интерфейс в боте и мини-приложении.

## 🔍 Причина

Логика определения языка была инвертирована:
- **Было**: Если язык НЕ входит в список русскоязычных → английский
- **Стало**: Если язык ВХОДИТ в список русскоязычных → русский

## ✅ Исправления

### 1. Бот (bot/webhook.php)
```php
// БЫЛО (неправильно):
if (!in_array($langCode, ['ru', 'be', 'uk', ...])) {
    $userLanguage = 'en';
}

// СТАЛО (правильно):
$userLanguage = 'en'; // По умолчанию английский
if (in_array($langCode, ['ru', 'be', 'uk', ...])) {
    $userLanguage = 'ru';
}
```

### 2. Мини-приложение (js/localization.js)
```javascript
// БЫЛО (неправильно):
if (langCode === 'ru' || langCode.startsWith('ru')) {
    this.currentLanguage = 'ru';
}

// СТАЛО (правильно):
const russianLanguageCodes = ['ru', 'be', 'uk', 'kk', 'ky', 'tg', 'uz', 'hy', 'az', 'ka', 'ro'];
this.currentLanguage = 'en'; // По умолчанию английский
if (russianLanguageCodes.includes(langCode)) {
    this.currentLanguage = 'ru';
}
```

### 3. API (api/getUserLanguage.php)
```php
// Добавлено логирование для отладки
error_log("getUserLanguage INFO: Определение языка для пользователя {$userId}");
error_log("getUserLanguage INFO: Данные пользователя из Telegram: " . json_encode($user));
```

### 4. Класс локализации (includes/Localization.php)
```php
// Унифицированная логика определения языка
$russianLanguageCodes = ['ru', 'be', 'uk', 'kk', 'ky', 'tg', 'uz', 'hy', 'az', 'ka', 'ro'];
$detectedLanguage = 'en'; // По умолчанию английский

if (in_array($langCode, $russianLanguageCodes)) {
    $detectedLanguage = 'ru';
}
```

## 🎯 Логика определения языка

### Приоритет определения:
1. **Сохраненный язык** - если пользователь уже зарегистрирован
2. **language_code из Telegram** - основной критерий
3. **country_code из Telegram** - дополнительный критерий
4. **Язык браузера** - fallback для мини-приложения
5. **Английский по умолчанию** - если ничего не подошло

### Русскоязычные коды:
- `ru` - Русский
- `be` - Белорусский  
- `uk` - Украинский
- `kk` - Казахский
- `ky` - Киргизский
- `tg` - Таджикский
- `uz` - Узбекский
- `hy` - Армянский
- `az` - Азербайджанский
- `ka` - Грузинский
- `ro` - Румынский (Молдова)

### Русскоязычные страны:
- `RU` - Россия
- `BY` - Беларусь
- `KZ` - Казахстан
- `KG` - Киргизия
- `TJ` - Таджикистан
- `UZ` - Узбекистан
- `AM` - Армения
- `AZ` - Азербайджан
- `GE` - Грузия
- `MD` - Молдова
- `UA` - Украина

## 🧪 Тестирование

Создан тест `test_language_detection.php` который проверяет:
- ✅ Английский пользователь (`en`) → английский
- ✅ Русский пользователь (`ru`) → русский  
- ✅ Белорусский пользователь (`be`) → русский
- ✅ Украинский пользователь (`uk`) → русский
- ✅ Казахский пользователь (`kk`) → русский
- ✅ Немецкий пользователь (`de`) → английский
- ✅ Французский пользователь (`fr`) → английский
- ✅ Пользователь без language_code → английский
- ✅ Определение по стране (RU) → русский
- ✅ Пользователь из США → английский

## 🔄 Сброс настроек

Создан скрипт `reset_user_languages.php` для сброса сохраненных языковых настроек пользователей, чтобы язык определился заново.

## 📝 Логирование

Добавлено подробное логирование процесса определения языка:
- В боте: `bot/bot.log`
- В API: логи PHP error_log
- В мини-приложении: консоль браузера

## 🎉 Результат

Теперь определение языка работает корректно:
- **Пользователи с `language_code: "en"`** → видят английский интерфейс
- **Пользователи с `language_code: "ru"`** → видят русский интерфейс
- **Пользователи с другими русскоязычными кодами** → видят русский интерфейс
- **Все остальные пользователи** → видят английский интерфейс по умолчанию

## 🔧 Отладка

Для отладки проблем с языком:
1. Проверьте логи бота: `bot/bot.log`
2. Проверьте консоль браузера в мини-приложении
3. Проверьте логи PHP на сервере
4. Используйте тестовый скрипт для проверки логики
