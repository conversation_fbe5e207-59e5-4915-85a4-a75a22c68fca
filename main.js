// === main.js ===
// ПОЛНАЯ ВЕРСИЯ с исправлением инлайнового display:none при анимации v6

// --- Глобальные Константы и Переменные ---
const API_BASE_URL = "./api";
const MY_PUB_ID = "944840";
const MY_APP_ID = "2122";
const DEBUG_MODE = false;
const BOT_USERNAME = "uniqpaid_bot"; // <-- !!! ИМЯ ВАШЕГО БОТА (БЕЗ @) !!!
const PAGE_TRANSITION_DURATION = 300; // мс, для CSS (должно совпадать)
const AUTO_RELOAD_AFTER_COUNTDOWN = true; // 🔄 Автоматический reload после таймера для обновления рекламы
const USE_SOFT_REFRESH = false; // 🔄 Использовать мягкое обновление (переинициализация SDK) вместо полного reload

// Ссылки на элементы DOM (все страницы/секции)
const mainContentEl = document.getElementById("main-content");
const earnSectionEl = document.getElementById("earn-section");
const friendsSectionEl = document.getElementById("friends-section");
const allPages = [mainContentEl, earnSectionEl, friendsSectionEl]; // Массив всех страниц

// Остальные элементы
const userNameEl = document.getElementById("user-name");
const balanceAmountEl = document.getElementById("balance-amount");
const headerBalanceInfoEl = document.getElementById("header-balance-info");
const watchAdButton = document.getElementById("openLinkButton");
const watchVideoButton = document.getElementById("watchVideoButton");
const openLinkButton = document.getElementById("openAdButton");
const statusMessageEl = document.getElementById("status-message");
const shareAppButton = document.getElementById("share-app-button");
const referralLinkInput = document.getElementById("referral-link-input");
const copyReferralButton = document.getElementById("copy-referral-button");
const earnBalanceAmountEl = document.getElementById("earn-balance-amount");
const availableWithdrawalEl = document.getElementById("available-withdrawal");
const minWithdrawalEl = document.getElementById("min-withdrawal");
const withdrawalAmountInput = document.getElementById("withdrawal-amount");
const withdrawalAddressInput = document.getElementById("withdrawal-address");
const cryptoCurrencySelect = document.getElementById("crypto-currency");
const requestWithdrawalButton = document.getElementById(
  "request-withdrawal-button"
);
const withdrawalErrorEl = document.getElementById("withdrawal-error");
const navHomeButton = document.getElementById("nav-home");
const navEarnButton = document.getElementById("nav-earn");
const navFriendsButton = document.getElementById("nav-friends");

// Глобальные переменные
const tg = window.Telegram.WebApp;
let adsController = null;
let currentUserId = null;
let currentUserBalance = 0;
let currentPageElement = mainContentEl; // Отслеживаем текущую страницу
let isTransitioning = false; // Флаг анимации

// Настройки вывода средств (получаем с сервера)
let minWithdrawalAmount = 0; // Нет минимальной суммы для вывода
let minBalanceForWithdrawal = 100; // Минимальный баланс для доступа к выводу

// Переменные для отслеживания состояния рекламы
let lastAdShownTime = 0;
let adCooldownTime = 3000; // 3 секунды между показами рекламы
let isAdShowing = false; // Флаг показа рекламы

// Переменные для счетчика обратного отсчета
let countdownTimer = null;
let isButtonPressed = false;
let coinValue = 0.001; // $0.001 за монету (должно соответствовать серверному значению)

// Типы рекламы
const AD_TYPES = {
  NATIVE_BANNER: 'native_banner',     // Баннер-превью (для верхней кнопки)
  INTERSTITIAL: 'interstitial',       // Полноэкранный баннер (для средней кнопки)
  REWARDED_VIDEO: 'rewarded_video'    // Видеореклама (для нижней кнопки)
};

// --- Вспомогательные Функции ---

/**
 * Отображает статусное сообщение.
 * @param {string} message Текст сообщения.
 * @param {'info' | 'success' | 'error'} type Тип сообщения.
 */
function showStatus(message, type = "info") {
  if (!statusMessageEl) {
    console.warn("Элемент status-message не найден.");
    return;
  }
  statusMessageEl.textContent = message;
  statusMessageEl.className = "status-message";
  if (type === "success") {
    statusMessageEl.classList.add("success");
  } else if (type === "error") {
    statusMessageEl.classList.add("error");
  }
  console.log(`Status [${type}]: ${message}`);
}

/**
 * Обновляет отображение баланса в нескольких местах.
 * @param {number | string} newBalance Новый баланс.
 */
function updateBalanceDisplay(newBalance) {
  currentUserBalance = parseInt(newBalance) || 0;
  if (balanceAmountEl) balanceAmountEl.textContent = currentUserBalance;
  if (earnBalanceAmountEl) earnBalanceAmountEl.textContent = currentUserBalance;
  if (availableWithdrawalEl)
    availableWithdrawalEl.textContent = currentUserBalance;
  console.log(`Баланс обновлен: ${currentUserBalance}`);
}

// --- Функции Переключения Видов с АНИМАЦИЕЙ (Исправленная Логика v6) ---

/**
 * Переключает видимую страницу с CSS-анимацией (v6 - сброс inline display).
 * @param {HTMLElement} nextPageElement Элемент новой страницы.
 * @param {HTMLElement} [activeNavButton] Кнопка навигации для активации.
 */
function switchPageAnimated(nextPageElement, activeNavButton) {
  if (
    !nextPageElement ||
    nextPageElement === currentPageElement ||
    isTransitioning
  ) {
    console.log(
      `Переключение отменено: next=${nextPageElement?.id}, current=${currentPageElement?.id}, transitioning=${isTransitioning}`
    );
    return;
  }
  console.log(
    `Анимация v6: ${currentPageElement?.id} -> ${nextPageElement.id}`
  );
  isTransitioning = true;
  const pageOutElement = currentPageElement;

  // 1. Обновляем кнопку навигации
  updateActiveNavButton(activeNavButton);

  // --- Подготовка новой страницы ---
  // СНАЧАЛА УБИРАЕМ КЛАСС СКРЫТИЯ И СБРАСЫВАЕМ ИНЛАЙНОВЫЙ DISPLAY
  nextPageElement.classList.remove("page-hidden");
  nextPageElement.style.display = ""; // <-- ВАЖНО: Сбрасываем инлайновый стиль!
  // Сбрасываем классы анимации на всякий случай
  nextPageElement.classList.remove(
    "page-leave-active",
    "page-enter-active",
    "page-enter"
  );
  // Ставим начальное состояние анимации входа
  nextPageElement.classList.add("page-enter");
  console.log(
    `Новая страница ${nextPageElement.id}: убран hidden, сброшен inline display, добавлен enter`
  );

  // --- Анимация ухода старой страницы ---
  if (pageOutElement) {
    pageOutElement.classList.remove("page-enter-active");
    pageOutElement.classList.add("page-leave-active"); // Запускаем анимацию ухода
    console.log(`Старая страница ${pageOutElement.id}: добавлен leave-active`);
  }

  // --- Запуск анимации входа новой страницы (после reflow) ---
  requestAnimationFrame(() => {
    nextPageElement.classList.remove("page-enter");
    nextPageElement.classList.add("page-enter-active");
    console.log(
      `Новая страница ${nextPageElement.id}: убран enter, добавлен enter-active`
    );
  });

  // 5. Обновляем текущую страницу СРАЗУ
  currentPageElement = nextPageElement;

  // 6. Завершение анимации и очистка классов
  setTimeout(() => {
    // Убираем классы анимации с НОВОЙ (теперь текущей) страницы
    currentPageElement.classList.remove("page-enter-active");
    console.log(`Новая страница ${currentPageElement.id}: убран enter-active`);

    // Прячем СТАРУЮ страницу (добавляем класс) и убираем класс анимации
    if (pageOutElement) {
      pageOutElement.classList.add("page-hidden"); // Добавляем класс для скрытия
      pageOutElement.classList.remove("page-leave-active");
      // Дополнительно можно сбросить инлайн стиль и у старой страницы
      pageOutElement.style.display = "";
      console.log(
        `Старая страница ${pageOutElement.id}: добавлен hidden, убран leave-active, сброшен inline display`
      );
    }

    isTransitioning = false; // Разрешаем следующее переключение
    console.log(`Анимация v6 завершена. Активна: ${currentPageElement.id}`);

    // Действия после переключения
    if (currentPageElement === friendsSectionEl) {
      generateReferralLink();
      loadReferralStats(); // Загружаем статистику рефералов при переходе на страницу друзей
    }
    if (currentPageElement === earnSectionEl) {
      updateWithdrawalSection();
    }
  }, PAGE_TRANSITION_DURATION); // Ждем время анимации
}

/** Показывает главную секцию */
function showMainContent() {
  switchPageAnimated(mainContentEl, navHomeButton);
}
/** Показывает секцию заработка */
function showEarnSection() {
  switchPageAnimated(earnSectionEl, navEarnButton);
}
/** Показывает секцию друзей */
function showFriendsSection() {
  switchPageAnimated(friendsSectionEl, navFriendsButton);
}

/** Обновляет активное состояние кнопок навигации */
function updateActiveNavButton(activeButton) {
  [navHomeButton, navEarnButton, navFriendsButton].forEach((button) => {
    if (button) button.classList.remove("active");
  });
  if (activeButton) {
    activeButton.classList.add("active");
  }
}

// --- Функции Взаимодействия с Бэкендом (PHP API) ---

/**
 * Применяет локализацию на основе языка пользователя
 */
async function applyUserLocalization() {
  try {
    console.log('[Localization] Применение локализации пользователя...');

    // Получаем язык пользователя с сервера
    const response = await fetch(`${API_BASE_URL}/getUserLanguage.php`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ initData: tg.initData }),
    });

    if (response.ok) {
      const data = await response.json();
      if (data.success && data.language) {
        console.log(`[Localization] Язык пользователя с сервера: ${data.language}`);

        // Устанавливаем язык в локализации
        if (window.appLocalization) {
          window.appLocalization.setLanguage(data.language);
          console.log(`[Localization] Язык установлен: ${data.language}`);

          // Принудительно применяем переводы
          window.appLocalization.applyTranslations();
          console.log('[Localization] Переводы применены к интерфейсу');
        }
      }
    } else {
      console.warn('[Localization] Не удалось получить язык пользователя с сервера');

      // Fallback: используем локальное определение языка
      if (window.appLocalization) {
        window.appLocalization.detectLanguage();
        window.appLocalization.applyTranslations();
        console.log('[Localization] Использовано локальное определение языка');
      }
    }
  } catch (error) {
    console.warn('[Localization] Ошибка применения локализации:', error);

    // Fallback: используем локальное определение языка
    if (window.appLocalization) {
      window.appLocalization.detectLanguage();
      window.appLocalization.applyTranslations();
      console.log('[Localization] Использован fallback на локальное определение');
    }
  }
}

/** Запрашивает данные пользователя с сервера. */
async function fetchUserData() {
  if (!tg.initData) {
    showStatus("Ошибка: Нет данных Telegram.", "error");
    tg.showAlert("Не удалось получить данные пользователя.");
    if (watchAdButton) watchAdButton.disabled = true;
    return;
  }
  showStatus("Загрузка данных...");
  try {
    const response = await fetch(`${API_BASE_URL}/getUserData.php`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ initData: tg.initData }),
    });
    if (!response.ok) {
      let eText = `Ошибка: ${response.status}`;
      try {
        const eData = await response.json();
        if (eData && eData.error) eText = `Ошибка: ${eData.error}`;
      } catch (e) {}
      throw new Error(eText);
    }
    const data = await response.json();
    if (data.error) throw new Error(data.error);
    currentUserId = data.userId;

    // Обновляем настройки вывода средств с сервера
    if (data.min_withdrawal !== undefined) minWithdrawalAmount = data.min_withdrawal;
    if (data.min_balance_for_withdrawal !== undefined) minBalanceForWithdrawal = data.min_balance_for_withdrawal;

    // Обновляем отображение минимальной суммы в HTML
    if (minWithdrawalEl) {
      if (minWithdrawalAmount > 0) {
        minWithdrawalEl.textContent = minWithdrawalAmount;
      } else {
        // Если нет минимальной суммы, скрываем или обновляем текст
        const parentHint = minWithdrawalEl.closest('.hint');
        if (parentHint) {
          parentHint.style.display = 'none';
        }
      }
    }

    if (userNameEl) {
      const u = tg.initDataUnsafe?.user;
      if (u?.username) userNameEl.textContent = `@${u.username}`;
      else if (u?.first_name)
        userNameEl.textContent =
          u.first_name + (u.last_name ? ` ${u.last_name}` : "");
      else userNameEl.textContent = `User ${currentUserId || "???"}`;
    }
    updateBalanceDisplay(data.balance);

    // 🌍 ПРИМЕНЯЕМ ЛОКАЛИЗАЦИЮ после загрузки данных пользователя
    await applyUserLocalization();

    showStatus("Данные загружены.", "success");
    setTimeout(() => {
      if (statusMessageEl.textContent === "Данные загружены.") showStatus("");
    }, 2000);
  } catch (error) {
    console.error("[Fetch User Data] Ошибка:", error);
    showStatus(`${error.message}`, "error");
    tg.showAlert(`${error.message}`);
    if (watchAdButton) watchAdButton.disabled = true;
  }
}

/** Отправляет запрос на сервер для записи просмотра рекламы. */
async function recordAdView(adType = 'default') {
  if (!tg.initData) {
    showStatus("Ошибка: Нет данных Telegram.", "error");
    tg.showAlert("Критическая ошибка: Невозможно засчитать награду.");
    return false;
  }

  console.log(`[API] Запись просмотра рекламы типа: ${adType}`);
  showStatus("Запись просмотра...");

  try {
    const response = await fetch(`${API_BASE_URL}/recordAdView.php`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        initData: tg.initData,
        adType: adType // Передаем тип рекламы для возможной разной награды
      }),
    });

    if (!response.ok) {
      let eText = `Ошибка: ${response.status}`;
      try {
        const eData = await response.json();
        if (eData && eData.error) eText = `Ошибка: ${eData.error}`;
      } catch (e) {}
      throw new Error(eText);
    }

    const data = await response.json();
    if (data.error) throw new Error(data.error);

    updateBalanceDisplay(data.newBalance);
    showStatus(`Награда зачислена! Баланс: ${data.newBalance}`, "success");
    tg.HapticFeedback.notificationOccurred("success");

    setTimeout(() => {
      if (statusMessageEl.textContent.startsWith("Награда зачислена"))
        showStatus("");
    }, 2500);

    return true;
  } catch (error) {
    console.error("[Record Ad View] Ошибка:", error);
    showStatus(`Ошибка записи: ${error.message}`, "error");
    tg.showAlert(`Не удалось засчитать награду: ${error.message}`);
    tg.HapticFeedback.notificationOccurred("error");
    return false;
  }
}

// --- Функции для расчета криптовалюты ---

/**
 * Рассчитывает сумму в криптовалюте с учетом комиссии
 * @param {number} coinAmount - Количество монет
 * @param {string} currency - Валюта
 * @returns {string} - Отформатированная сумма
 */
function calculateCryptoAmount(coinAmount, currency) {
  if (!coinAmount || coinAmount <= 0) return "0";

  // Конвертируем монеты в доллары
  const dollarAmount = coinAmount * coinValue;

  // Получаем данные о валюте из калькулятора
  const currencyInfo = currencyData[currency];
  if (!currencyInfo) {
    // Fallback для старых курсов
    const exchangeRates = {
      'usdttrc20': 1.0,
      'btc': 0.000025,
      'eth': 0.0004,
      'trx': 8.5
    };
    const rate = exchangeRates[currency] || 1.0;
    return (dollarAmount * rate * 0.995).toFixed(4); // 0.5% комиссия
  }

  // Вычитаем сетевую комиссию
  const afterFee = dollarAmount - currencyInfo.networkFee;

  if (afterFee <= 0) {
    return "0 (комиссия больше суммы)";
  }

  // Примерные курсы криптовалют
  const exchangeRates = {
    'usdttrc20': 1.0,
    'btc': 0.000025,
    'eth': 0.0004,
    'trx': 8.5
  };

  const rate = exchangeRates[currency] || 1.0;
  let cryptoAmount = afterFee * rate;

  // Форматируем в зависимости от валюты
  if (currency === 'btc') {
    return cryptoAmount.toFixed(8);
  } else if (currency === 'eth') {
    return cryptoAmount.toFixed(6);
  } else {
    return cryptoAmount.toFixed(4);
  }
}

/**
 * Обновляет поле суммы в криптовалюте
 */
function updateCryptoAmountField() {
  const withdrawalAmount = parseFloat(withdrawalAmountInput?.value) || 0;
  const selectedCurrency = cryptoCurrencySelect?.value || 'usdttrc20';
  const cryptoAmountField = document.getElementById('crypto-amount');

  if (cryptoAmountField) {
    const cryptoAmount = calculateCryptoAmount(withdrawalAmount, selectedCurrency);
    const currencyName = cryptoCurrencySelect?.options[cryptoCurrencySelect.selectedIndex]?.text || 'USDT';
    cryptoAmountField.value = `${cryptoAmount} ${currencyName.split(' ')[0]}`;
  }
}

// --- Функции для счетчика обратного отсчета ---

/**
 * Запускает счетчик обратного отсчета на кнопке
 * @param {HTMLElement} button - Кнопка для блокировки
 * @param {number} seconds - Количество секунд для отсчета (по умолчанию 20)
 */
function startCountdown(button, seconds = 20) {
  if (!button || isButtonPressed) return;

  isButtonPressed = true;

  // Добавляем класс нажатой кнопки
  button.classList.add('pressed');

  // Блокируем все кнопки рекламы
  disableAllAdButtons();

  // Создаем элемент счетчика
  const countdownOverlay = document.createElement('div');
  countdownOverlay.className = 'countdown-overlay';
  countdownOverlay.textContent = seconds;
  button.appendChild(countdownOverlay);

  let remainingTime = seconds;

  countdownTimer = setInterval(() => {
    remainingTime--;
    countdownOverlay.textContent = remainingTime;

    if (remainingTime <= 0) {
      clearInterval(countdownTimer);
      countdownTimer = null;

      // Убираем счетчик и возвращаем кнопку в исходное состояние
      button.removeChild(countdownOverlay);
      button.classList.remove('pressed');

      // Разблокируем все кнопки рекламы
      enableAllAdButtons();

      isButtonPressed = false;

      // 🔄 АВТОМАТИЧЕСКОЕ ОБНОВЛЕНИЕ РЕКЛАМНЫХ БЛОКОВ
      if (AUTO_RELOAD_AFTER_COUNTDOWN) {
        if (USE_SOFT_REFRESH) {
          // Мягкое обновление - переинициализация SDK без reload страницы
          console.log("[Countdown] Таймер завершен, выполняем мягкое обновление рекламы");
          setTimeout(() => {
            refreshAdBlocks();
          }, 500);
        } else {
          // Полный reload страницы
          console.log("[Countdown] Таймер завершен, выполняем reload страницы для обновления рекламы");

          // Показываем уведомление о reload
          showStatus("Обновляем рекламные блоки...", "info");

          // Небольшая задержка перед reload для показа сообщения
          setTimeout(() => {
            try {
              // Сохраняем текущую страницу в localStorage перед reload
              if (currentPageElement) {
                localStorage.setItem('lastActivePage', currentPageElement.id);
              }

              // Выполняем reload страницы
              window.location.reload();
            } catch (error) {
              console.warn("[Countdown] Ошибка при reload:", error);
              // Если reload не удался, пробуем мягкое обновление
              refreshAdBlocks();
            }
          }, 1000);
        }
      } else {
        console.log("[Countdown] Таймер завершен, автоматическое обновление отключено");
        // Просто показываем сообщение о том, что можно смотреть рекламу снова
        showStatus("Можете смотреть рекламу снова!", "success");
        setTimeout(() => {
          if (statusMessageEl.textContent === "Можете смотреть рекламу снова!") {
            showStatus("", "");
          }
        }, 3000);
      }
    }
  }, 1000);
}

/**
 * Блокирует все кнопки рекламы
 */
function disableAllAdButtons() {
  if (watchAdButton) watchAdButton.disabled = true;
  if (watchVideoButton) watchVideoButton.disabled = true;
  if (openLinkButton) openLinkButton.disabled = true;
}

/**
 * Разблокирует все кнопки рекламы
 */
function enableAllAdButtons() {
  if (watchAdButton) watchAdButton.disabled = false;
  if (watchVideoButton) watchVideoButton.disabled = false;
  if (openLinkButton) openLinkButton.disabled = false;
}

/**
 * 🔄 Обновляет рекламные блоки без полного reload страницы
 */
function refreshAdBlocks() {
  console.log("[Ad Refresh] Попытка обновления рекламных блоков...");

  try {
    // Показываем статус обновления
    showStatus("Обновляем рекламу...", "info");

    // Если есть активный контроллер рекламы, пробуем его переинициализировать
    if (adsController && typeof adsController.initialize === 'function') {
      console.log("[Ad Refresh] Переинициализация RichAds SDK...");

      adsController.initialize({
        pubId: MY_PUB_ID,
        appId: MY_APP_ID,
        debug: DEBUG_MODE,
      });

      setTimeout(() => {
        showStatus("Реклама обновлена!", "success");
        setTimeout(() => {
          if (statusMessageEl.textContent === "Реклама обновлена!") {
            showStatus("", "");
          }
        }, 2000);
      }, 1000);

    } else {
      // Если контроллер недоступен, пробуем полную переинициализацию
      console.log("[Ad Refresh] Полная переинициализация рекламы...");
      initializeRichAds();
    }

  } catch (error) {
    console.warn("[Ad Refresh] Ошибка обновления рекламы:", error);
    showStatus("Ошибка обновления рекламы", "error");
    setTimeout(() => {
      showStatus("", "");
    }, 3000);
  }
}

/**
 * Показывает сообщение об отсутствии рекламы
 */
function showNoAdMessage() {
  const message = window.appLocalization ? window.appLocalization.get('ads.no_ads_available') : "Нет доступной рекламы. Попробуйте позже.";
  showStatus(message, "info");
  setTimeout(() => {
    if (statusMessageEl.textContent === message) {
      showStatus("");
    }
  }, 3000);
}

// --- Функции Работы с RichAds SDK ---

/** Инициализирует RichAds SDK. */
function initializeRichAds() {
  showStatus("Инициализация рекламы...");
  try {
    console.log("[RichAds Init] Проверка доступности SDK...");

    // Проверяем наличие SDK
    if (typeof TelegramAdsController !== "function" && typeof TelegramAdsController !== "object") {
      console.warn("[RichAds Init] SDK не найден, пробуем загрузить скрипт вручную");

      // Пробуем загрузить скрипт вручную
      const script = document.createElement('script');
      script.src = "https://richinfo.co/richpartners/telegram/js/tg-ob.js";
      document.head.appendChild(script);

      // Устанавливаем таймаут для повторной попытки
      setTimeout(() => {
        console.log("[RichAds Init] Повторная попытка инициализации после загрузки скрипта");
        initializeRichAdsAfterLoad();
      }, 1000);

      return;
    }

    // Если SDK доступен, продолжаем инициализацию
    initializeRichAdsAfterLoad();

  } catch (error) {
    console.error("[RichAds Init] КРИТИЧЕСКАЯ ОШИБКА:", error);
    showStatus(`Ошибка инициализации рекламы: ${error.message}`, "error");
    tg.showAlert(`Ошибка модуля рекламы:\n${error.message}`);

    // Блокируем все кнопки рекламы при ошибке
    if (watchAdButton) watchAdButton.disabled = true;
    if (watchVideoButton) watchVideoButton.disabled = true;
    if (openLinkButton) openLinkButton.disabled = true;
  }
}

/** Инициализирует RichAds SDK после загрузки скрипта. */
function initializeRichAdsAfterLoad() {
  try {
    console.log("[RichAds Init] Попытка создания экземпляра SDK...");

    // Проверяем, доступен ли SDK как функция
    if (typeof TelegramAdsController === "function") {
      console.log("[RichAds Init] SDK доступен как функция, создаем экземпляр");
      adsController = new TelegramAdsController();
      window.TelegramAdsController = adsController;
    }
    // Проверяем, доступен ли SDK как объект
    else if (typeof TelegramAdsController === "object" && TelegramAdsController !== null) {
      console.log("[RichAds Init] SDK доступен как объект, используем его");
      adsController = TelegramAdsController;
    }
    else {
      throw new Error("SDK RichAds (TelegramAdsController) не найден или имеет неверный тип");
    }

    // Проверяем наличие метода initialize
    if (!adsController || typeof adsController.initialize !== 'function') {
      throw new Error("Метод initialize не найден в SDK");
    }

    // Инициализируем SDK
    console.log("[RichAds Init] Вызов метода initialize с параметрами:", {
      pubId: MY_PUB_ID,
      appId: MY_APP_ID,
      debug: DEBUG_MODE
    });

    adsController.initialize({
      pubId: MY_PUB_ID,
      appId: MY_APP_ID,
      debug: DEBUG_MODE,
    });

    console.log("[RichAds Init] Инициализация SDK успешно вызвана");
    showStatus("Реклама готова.", "success");

    // Разблокируем все кнопки рекламы
    if (watchAdButton) watchAdButton.disabled = false;
    if (watchVideoButton) watchVideoButton.disabled = false;
    if (openLinkButton) openLinkButton.disabled = false;

    setTimeout(() => {
      if (statusMessageEl.textContent === "Реклама готова.") showStatus("");
    }, 2000);
  } catch (error) {
    console.error("[RichAds Init] ОШИБКА:", error);
    showStatus(`Ошибка инициализации рекламы: ${error.message}`, "error");
    tg.showAlert(`Ошибка модуля рекламы:\n${error.message}`);

    // Блокируем все кнопки рекламы при ошибке
    if (watchAdButton) watchAdButton.disabled = true;
    if (watchVideoButton) watchVideoButton.disabled = true;
    if (openLinkButton) openLinkButton.disabled = true;
  }
}

/** Обрабатывает нажатие на кнопку "Смотреть рекламу" - автопереход по баннеру-превью. */
function handleWatchAdClick() {
  // Проверка на доступность рекламы
  if (!adsController) {
    showStatus("Ошибка: Реклама не готова.", "error");
    tg.showAlert("Модуль рекламы не загружен.");
    return;
  }

  // Проверка на переход между страницами
  if (isTransitioning) {
    console.warn("Клик по рекламе во время перехода страницы - игнорируем.");
    return;
  }

  // Проверка на кулдаун между показами рекламы
  const currentTime = Date.now();
  if (isAdShowing) {
    console.warn("[RichAds] Реклама уже показывается, игнорируем клик");
    showStatus("Подождите, реклама уже показывается...", "info");
    return;
  }

  if (currentTime - lastAdShownTime < adCooldownTime) {
    console.warn("[RichAds] Слишком частые запросы рекламы, игнорируем клик");
    showStatus("Подождите немного перед следующим просмотром...", "info");
    return;
  }

  // Устанавливаем флаг показа рекламы
  isAdShowing = true;
  lastAdShownTime = currentTime;

  showStatus("Запрос баннера-превью с автопереходом...");
  if (watchAdButton) watchAdButton.disabled = true;
  tg.HapticFeedback.impactOccurred("light");

  try {
    // Используем triggerNativeNotification с параметром true для автоперехода по баннеру-превью
    console.log("[RichAds] Запуск баннера-превью с автопереходом...");

    // Проверяем, доступен ли метод triggerNativeNotification
    if (typeof adsController.triggerNativeNotification === 'function') {
      console.log("[RichAds] Вызов triggerNativeNotification(true) для автоперехода по баннеру-превью");

      // Вызываем метод с параметром true для автоперехода
      adsController.triggerNativeNotification(true)
        .then((result) => {
          console.log("[RichAds] Успешный автопереход по баннеру-превью:", result);
          showStatus("Баннер-превью просмотрен! Начисляем награду...", "info");
          return recordAdView(AD_TYPES.NATIVE_BANNER);
        })
        .then((success) => {
          if (success) {
            // Запускаем счетчик после успешного просмотра
            startCountdown(watchAdButton);
            tg.showPopup({
              title: window.appLocalization ? window.appLocalization.get('common.success') : "Успех!",
              message: window.appLocalization ? window.appLocalization.get('ads.ad_reward_credited') : "Награда за переход по баннеру-превью зачислена!",
              buttons: [{ type: "ok", text: window.appLocalization ? window.appLocalization.get('common.excellent') : "Отлично" }]
            });
          } else {
            // Если награда не зачислена, все равно запускаем счетчик
            startCountdown(watchAdButton);
          }
        })
        .catch((error) => {
          console.warn("[RichAds] Ошибка автоперехода по баннеру-превью:", error);

          // Проверяем, есть ли реклама
          if (error.message && error.message.toLowerCase().includes('no ad')) {
            showNoAdMessage();
            startCountdown(watchAdButton);
          } else {
            // Пробуем альтернативный метод при ошибке
            if (typeof adsController.triggerInterstitialBanner === 'function') {
              console.log("[RichAds] Пробуем альтернативный метод triggerInterstitialBanner(true)");

              adsController.triggerInterstitialBanner(true)
                .then((result) => {
                  console.log("[RichAds] Успешный автопереход по баннеру (альтернативный метод):", result);
                  showStatus("Реклама просмотрена! Начисляем награду...", "info");
                  return recordAdView(AD_TYPES.NATIVE_BANNER);
                })
                .then((success) => {
                  if (success) {
                    startCountdown(watchAdButton);
                    tg.showPopup({
                      title: "Успех!",
                      message: "Награда за переход по баннеру зачислена!",
                      buttons: [{ type: "ok", text: "Отлично" }]
                    });
                  } else {
                    startCountdown(watchAdButton);
                  }
                })
                .catch((altError) => {
                  handleAdError(altError);
                  startCountdown(watchAdButton);
                })
                .finally(() => {
                  isAdShowing = false;
                });
            } else {
              handleAdError(error);
              startCountdown(watchAdButton);
              isAdShowing = false;
            }
          }
        })
        .finally(() => {
          if (watchAdButton) watchAdButton.disabled = false;
          console.log("[RichAds] Операция автоперехода по баннеру-превью завершена");
          isAdShowing = false;
        });
    }
    // Если triggerNativeNotification недоступен, пробуем triggerInterstitialBanner
    else if (typeof adsController.triggerInterstitialBanner === 'function') {
      console.log("[RichAds] Метод triggerNativeNotification недоступен, пробуем triggerInterstitialBanner(true)");

      adsController.triggerInterstitialBanner(true)
        .then((result) => {
          console.log("[RichAds] Успешный автопереход по баннеру:", result);
          showStatus("Реклама просмотрена! Начисляем награду...", "info");
          return recordAdView(AD_TYPES.NATIVE_BANNER);
        })
        .then((success) => {
          if (success) {
            tg.showPopup({
              title: "Успех!",
              message: "Награда за переход по баннеру зачислена!",
              buttons: [{ type: "ok", text: "Отлично" }]
            });
          }
        })
        .catch((error) => {
          handleAdError(error);
        })
        .finally(() => {
          if (watchAdButton) watchAdButton.disabled = false;
          console.log("[RichAds] Операция автоперехода по баннеру завершена");
          isAdShowing = false;
        });
    } else {
      // Если оба метода недоступны, выдаем ошибку
      throw new Error("Методы показа баннера-превью недоступны");
    }
  } catch (error) {
    console.error("[RichAds] Критическая ошибка при вызове баннера-превью:", error);
    showStatus(`Ошибка показа баннера-превью: ${error.message}`, "error");
    tg.showAlert(`Ошибка показа баннера-превью: ${error.message}`);
    if (watchAdButton) watchAdButton.disabled = false;
    isAdShowing = false;
  }

  // Функция для обработки ошибок рекламы
  function handleAdError(error) {
    console.warn("[RichAds] Ошибка показа баннера-превью:", error);
    let reason = "Неизвестная ошибка";
    let userFriendlyMessage = "В данный момент баннер-превью недоступен";

    if (error instanceof Error) {
      reason = error.message;

      // Обработка типичных ошибок с более понятными сообщениями
      if (reason.includes("Cannot read properties of null") ||
          reason.includes("undefined") ||
          reason.includes("length")) {
        userFriendlyMessage = "В данный момент баннер-превью недоступен";
      } else if (reason.includes("timeout") || reason.includes("time out")) {
        userFriendlyMessage = "Превышено время ожидания ответа от рекламной сети";
      } else if (reason.includes("network") || reason.includes("connection")) {
        userFriendlyMessage = "Проблема с сетевым подключением";
      } else {
        userFriendlyMessage = "Не удалось показать баннер-превью. Попробуйте позже";
      }
    } else if (typeof error === 'string' && error.trim()) {
      reason = error.trim();
      userFriendlyMessage = "Не удалось показать баннер-превью. Попробуйте позже";
    } else if (error && typeof error === 'object') {
      try {
        reason = JSON.stringify(error);
        userFriendlyMessage = "В данный момент баннер-превью недоступен";
      } catch (e) {
        reason = "Баннер-превью недоступен или был закрыт";
        userFriendlyMessage = "В данный момент баннер-превью недоступен";
      }
    }

    // Записываем техническую ошибку в консоль
    console.warn(`[RichAds] Техническая причина ошибки: ${reason}`);

    // Показываем пользователю понятное сообщение
    showStatus(userFriendlyMessage, "error");
    tg.showAlert(userFriendlyMessage);
    tg.HapticFeedback.notificationOccurred("warning");
  }
}

/** Обрабатывает нажатие на кнопку "Смотреть видео" - показ видеорекламы. */
function handleWatchVideoClick() {
  // Проверка на доступность рекламы
  if (!adsController) {
    showStatus("Ошибка: Реклама не готова.", "error");
    tg.showAlert("Модуль рекламы не загружен.");
    return;
  }

  // Проверка на переход между страницами
  if (isTransitioning) {
    console.warn("Клик по видео во время перехода страницы - игнорируем.");
    return;
  }

  // Проверка на кулдаун между показами рекламы
  const currentTime = Date.now();
  if (isAdShowing) {
    console.warn("[RichAds] Реклама уже показывается, игнорируем клик");
    showStatus("Подождите, реклама уже показывается...", "info");
    return;
  }

  if (currentTime - lastAdShownTime < adCooldownTime) {
    console.warn("[RichAds] Слишком частые запросы рекламы, игнорируем клик");
    showStatus("Подождите немного перед следующим просмотром...", "info");
    return;
  }

  // Устанавливаем флаг показа рекламы
  isAdShowing = true;
  lastAdShownTime = currentTime;

  showStatus("Запрос видеорекламы...");
  if (watchVideoButton) watchVideoButton.disabled = true;
  tg.HapticFeedback.impactOccurred("light");

  try {
    // Проверяем доступные методы для показа видеорекламы
    console.log("[RichAds] Запуск видеорекламы...");

    // Проверяем, доступен ли метод triggerInterstitialVideo
    if (typeof adsController.triggerInterstitialVideo === 'function') {
      console.log("[RichAds] Вызов triggerInterstitialVideo для показа видеорекламы");

      // Вызываем метод для показа видеорекламы
      adsController.triggerInterstitialVideo()
        .then((result) => {
          console.log("[RichAds] Успешный показ видеорекламы:", result);
          showStatus("Видеореклама просмотрена! Начисляем награду...", "info");
          return recordAdView('rewarded_video_view');
        })
        .then((success) => {
          if (success) {
            startCountdown(watchVideoButton);
            tg.showPopup({
              title: "Успех!",
              message: "Награда за просмотр видео зачислена!",
              buttons: [{ type: "ok", text: "Отлично" }]
            });
          } else {
            startCountdown(watchVideoButton);
          }
        })
        .catch((error) => {
          console.warn("[RichAds] Ошибка показа видеорекламы:", error);

          // Пробуем альтернативный метод для видеорекламы при ошибке
          if (typeof adsController.showRewardedVideo === 'function') {
            console.log("[RichAds] Пробуем альтернативный метод showRewardedVideo для показа видеорекламы");

            // Используем специальный метод для показа видеорекламы
            adsController.showRewardedVideo()
              .then((result) => {
                console.log("[RichAds] Успешный показ видеорекламы (альтернативный метод):", result);
                showStatus("Видеореклама просмотрена! Начисляем награду...", "info");
                return recordAdView('rewarded_video_view');
              })
              .then((success) => {
                if (success) {
                  tg.showPopup({
                    title: "Успех!",
                    message: "Награда за просмотр видео зачислена!",
                    buttons: [{ type: "ok", text: "Отлично" }]
                  });
                }
              })
              .catch((altError) => {
                handleVideoError(altError);
                startCountdown(watchVideoButton);
              })
              .finally(() => {
                isAdShowing = false;
              });
          } else {
            handleVideoError(error);
            startCountdown(watchVideoButton);
            isAdShowing = false;
          }
        })
        .finally(() => {
          if (watchVideoButton) watchVideoButton.disabled = false;
          console.log("[RichAds] Операция показа видеорекламы завершена");
          isAdShowing = false;
        });
    }
    // Если triggerInterstitialVideo недоступен, пробуем showRewardedVideo
    else if (typeof adsController.showRewardedVideo === 'function') {
      console.log("[RichAds] Метод triggerInterstitialVideo недоступен, пробуем showRewardedVideo");

      // Используем специальный метод для показа видеорекламы
      adsController.showRewardedVideo()
        .then((result) => {
          console.log("[RichAds] Успешный показ видеорекламы (альтернативный метод):", result);
          showStatus("Видеореклама просмотрена! Начисляем награду...", "info");
          return recordAdView('rewarded_video_view');
        })
        .then((success) => {
          if (success) {
            tg.showPopup({
              title: "Успех!",
              message: "Награда за просмотр видео зачислена!",
              buttons: [{ type: "ok", text: "Отлично" }]
            });
          }
        })
        .catch((error) => {
          handleVideoError(error);
        })
        .finally(() => {
          if (watchVideoButton) watchVideoButton.disabled = false;
          console.log("[RichAds] Операция показа видеорекламы завершена");
          isAdShowing = false;
        });
    }
    // Если предыдущие методы недоступны, пробуем triggerRewardedVideo
    else if (typeof adsController.triggerRewardedVideo === 'function') {
      console.log("[RichAds] Пробуем метод triggerRewardedVideo для показа видеорекламы");

      // Используем специальный метод для показа видеорекламы
      adsController.triggerRewardedVideo()
        .then((result) => {
          console.log("[RichAds] Успешный показ видеорекламы (альтернативный метод):", result);
          showStatus("Видеореклама просмотрена! Начисляем награду...", "info");
          return recordAdView('rewarded_video_view');
        })
        .then((success) => {
          if (success) {
            tg.showPopup({
              title: "Успех!",
              message: "Награда за просмотр видео зачислена!",
              buttons: [{ type: "ok", text: "Отлично" }]
            });
          }
        })
        .catch((error) => {
          handleVideoError(error);
        })
        .finally(() => {
          if (watchVideoButton) watchVideoButton.disabled = false;
          console.log("[RichAds] Операция показа видеорекламы завершена");
          isAdShowing = false;
        });
    } else {
      // Если все методы недоступны, выдаем ошибку
      throw new Error("Методы показа видеорекламы недоступны");
    }
  } catch (error) {
    console.error("[RichAds] Критическая ошибка при вызове видеорекламы:", error);
    showStatus(`Ошибка показа видеорекламы: ${error.message}`, "error");
    tg.showAlert(`Ошибка показа видеорекламы: ${error.message}`);
    if (watchVideoButton) watchVideoButton.disabled = false;
    isAdShowing = false;
  }

  // Функция для обработки ошибок видеорекламы
  function handleVideoError(error) {
    console.warn("[RichAds] Ошибка показа видеорекламы:", error);
    let reason = "Неизвестная ошибка";
    let userFriendlyMessage = "В данный момент видеореклама недоступна";

    if (error instanceof Error) {
      reason = error.message;

      // Проверяем, есть ли реклама
      if (reason && reason.toLowerCase().includes('no ad')) {
        showNoAdMessage();
        return;
      }

      // Обработка типичных ошибок с более понятными сообщениями
      if (reason.includes("Cannot read properties of null") ||
          reason.includes("undefined") ||
          reason.includes("length")) {
        userFriendlyMessage = "В данный момент видеореклама недоступна";
      } else if (reason.includes("timeout") || reason.includes("time out")) {
        userFriendlyMessage = "Превышено время ожидания ответа от рекламной сети";
      } else if (reason.includes("network") || reason.includes("connection")) {
        userFriendlyMessage = "Проблема с сетевым подключением";
      } else {
        userFriendlyMessage = "Не удалось показать видеорекламу. Попробуйте позже";
      }
    } else if (typeof error === 'string' && error.trim()) {
      reason = error.trim();

      // Проверяем, есть ли реклама
      if (reason.toLowerCase().includes('no ad')) {
        showNoAdMessage();
        return;
      }

      userFriendlyMessage = "Не удалось показать видеорекламу. Попробуйте позже";
    } else if (error && typeof error === 'object') {
      try {
        reason = JSON.stringify(error);
        userFriendlyMessage = "В данный момент видеореклама недоступна";
      } catch (e) {
        reason = "Видеореклама недоступна или была закрыта";
        userFriendlyMessage = "В данный момент видеореклама недоступна";
      }
    }

    // Записываем техническую ошибку в консоль
    console.warn(`[RichAds] Техническая причина ошибки: ${reason}`);

    // Показываем пользователю понятное сообщение
    showStatus(userFriendlyMessage, "error");
    tg.showAlert(userFriendlyMessage);
    tg.HapticFeedback.notificationOccurred("warning");
  }
}

/** Обрабатывает нажатие на кнопку "Открыть ссылку" - показ полноэкранного баннера. */
function handleOpenLinkClick() {
  // Проверка на доступность рекламы
  if (!adsController) {
    showStatus("Ошибка: Реклама не готова.", "error");
    tg.showAlert("Модуль рекламы не загружен.");
    return;
  }

  // Проверка на переход между страницами
  if (isTransitioning) {
    console.warn("Клик по ссылке во время перехода страницы - игнорируем.");
    return;
  }

  // Проверка на кулдаун между показами рекламы
  const currentTime = Date.now();
  if (isAdShowing) {
    console.warn("[RichAds] Реклама уже показывается, игнорируем клик");
    showStatus("Подождите, реклама уже показывается...", "info");
    return;
  }

  if (currentTime - lastAdShownTime < adCooldownTime) {
    console.warn("[RichAds] Слишком частые запросы рекламы, игнорируем клик");
    showStatus("Подождите немного перед следующим просмотром...", "info");
    return;
  }

  // Устанавливаем флаг показа рекламы
  isAdShowing = true;
  lastAdShownTime = currentTime;

  showStatus("Запрос полноэкранного баннера...");
  if (openLinkButton) openLinkButton.disabled = true;
  tg.HapticFeedback.impactOccurred("light");

  try {
    // Проверяем доступные методы для показа полноэкранного баннера
    console.log("[RichAds] Запуск полноэкранного баннера...");

    // Сначала проверяем наличие метода triggerInterstitialBanner для показа полноразмерного баннера
    if (typeof adsController.triggerInterstitialBanner === 'function') {
      console.log("[RichAds] Вызов triggerInterstitialBanner(false) для показа полноэкранного баннера без автоперехода");

      // Вызываем метод с параметром false для показа баннера без автоперехода
      adsController.triggerInterstitialBanner(false)
        .then((result) => {
          console.log("[RichAds] Успешный показ полноэкранного баннера:", result);
          showStatus("Полноэкранный баннер просмотрен! Начисляем награду...", "info");
          return recordAdView('interstitial_banner_view');
        })
        .then((success) => {
          if (success) {
            startCountdown(openLinkButton);
            tg.showPopup({
              title: "Успех!",
              message: "Награда за просмотр полноэкранного баннера зачислена!",
              buttons: [{ type: "ok", text: "Отлично" }]
            });
          } else {
            startCountdown(openLinkButton);
          }
        })
        .catch((error) => {
          console.warn("[RichAds] Ошибка показа полноэкранного баннера:", error);

          // Проверяем, есть ли реклама
          if (error.message && error.message.toLowerCase().includes('no ad')) {
            showNoAdMessage();
            startCountdown(openLinkButton);
          } else {
            // Пробуем другие методы при ошибке
            tryAlternativeMethods(error);
          }
        })
        .finally(() => {
          if (openLinkButton) openLinkButton.disabled = false;
          console.log("[RichAds] Операция показа полноэкранного баннера завершена");
          isAdShowing = false;
        });
      return; // Выходим из функции, так как уже вызвали нужный метод
    }

    // Выводим все доступные методы в консоль для отладки
    const availableMethods = Object.keys(adsController).filter(key => typeof adsController[key] === 'function');
    console.log("[RichAds] Доступные методы в adsController:", availableMethods);

    // Ищем все методы, которые могут быть связаны с полноэкранной рекламой
    const interstitialMethods = availableMethods.filter(method =>
      method.toLowerCase().includes('interstitial') ||
      method.toLowerCase().includes('fullscreen') ||
      method.toLowerCase().includes('full') ||
      method.toLowerCase().includes('screen')
    );
    console.log("[RichAds] Потенциальные методы для полноэкранной рекламы:", interstitialMethods);

    // Проверяем наличие метода triggerInterstitial
    if (typeof adsController.triggerInterstitial === 'function') {
      console.log("[RichAds] Вызов triggerInterstitial() для показа полноэкранного баннера");

      // Вызываем метод для показа полноэкранного баннера
      adsController.triggerInterstitial()
        .then((result) => {
          console.log("[RichAds] Успешный показ полноэкранного баннера:", result);
          showStatus("Полноэкранный баннер просмотрен! Начисляем награду...", "info");
          return recordAdView('interstitial_banner_view');
        })
        .then((success) => {
          if (success) {
            tg.showPopup({
              title: "Успех!",
              message: "Награда за просмотр полноэкранного баннера зачислена!",
              buttons: [{ type: "ok", text: "Отлично" }]
            });
          }
        })
        .catch((error) => {
          console.warn("[RichAds] Ошибка показа полноэкранного баннера:", error);

          // Пробуем альтернативный метод при ошибке
          tryAlternativeMethods(error);
        })
        .finally(() => {
          if (openLinkButton) openLinkButton.disabled = false;
          console.log("[RichAds] Операция показа полноэкранного баннера завершена");
          isAdShowing = false;
        });
    }
    // Проверяем наличие метода triggerFullscreenBanner
    else if (typeof adsController.triggerFullscreenBanner === 'function') {
      console.log("[RichAds] Вызов triggerFullscreenBanner() для показа полноэкранного баннера");

      // Вызываем метод для показа полноэкранного баннера
      adsController.triggerFullscreenBanner()
        .then((result) => {
          console.log("[RichAds] Успешный показ полноэкранного баннера:", result);
          showStatus("Полноэкранный баннер просмотрен! Начисляем награду...", "info");
          return recordAdView('interstitial_banner_view');
        })
        .then((success) => {
          if (success) {
            tg.showPopup({
              title: "Успех!",
              message: "Награда за просмотр полноэкранного баннера зачислена!",
              buttons: [{ type: "ok", text: "Отлично" }]
            });
          }
        })
        .catch((error) => {
          console.warn("[RichAds] Ошибка показа полноэкранного баннера:", error);

          // Пробуем альтернативный метод при ошибке
          tryAlternativeMethods(error);
        })
        .finally(() => {
          if (openLinkButton) openLinkButton.disabled = false;
          console.log("[RichAds] Операция показа полноэкранного баннера завершена");
          isAdShowing = false;
        });
    }
    // Проверяем наличие метода showInterstitial
    else if (typeof adsController.showInterstitial === 'function') {
      console.log("[RichAds] Вызов showInterstitial() для показа полноэкранного баннера");

      // Вызываем метод для показа полноэкранного баннера
      adsController.showInterstitial()
        .then((result) => {
          console.log("[RichAds] Успешный показ полноэкранного баннера:", result);
          showStatus("Полноэкранный баннер просмотрен! Начисляем награду...", "info");
          return recordAdView('interstitial_banner_view');
        })
        .then((success) => {
          if (success) {
            tg.showPopup({
              title: "Успех!",
              message: "Награда за просмотр полноэкранного баннера зачислена!",
              buttons: [{ type: "ok", text: "Отлично" }]
            });
          }
        })
        .catch((error) => {
          console.warn("[RichAds] Ошибка показа полноэкранного баннера:", error);

          // Пробуем альтернативный метод при ошибке
          tryAlternativeMethods(error);
        })
        .finally(() => {
          if (openLinkButton) openLinkButton.disabled = false;
          console.log("[RichAds] Операция показа полноэкранного баннера завершена");
          isAdShowing = false;
        });
    }
    // Проверяем наличие метода displayInterstitial
    else if (typeof adsController.displayInterstitial === 'function') {
      console.log("[RichAds] Вызов displayInterstitial() для показа полноэкранного баннера");

      // Вызываем метод для показа полноэкранного баннера
      adsController.displayInterstitial()
        .then((result) => {
          console.log("[RichAds] Успешный показ полноэкранного баннера:", result);
          showStatus("Полноэкранный баннер просмотрен! Начисляем награду...", "info");
          return recordAdView('interstitial_banner_view');
        })
        .then((success) => {
          if (success) {
            tg.showPopup({
              title: "Успех!",
              message: "Награда за просмотр полноэкранного баннера зачислена!",
              buttons: [{ type: "ok", text: "Отлично" }]
            });
          }
        })
        .catch((error) => {
          console.warn("[RichAds] Ошибка показа полноэкранного баннера:", error);

          // Пробуем альтернативный метод при ошибке
          tryAlternativeMethods(error);
        })
        .finally(() => {
          if (openLinkButton) openLinkButton.disabled = false;
          console.log("[RichAds] Операция показа полноэкранного баннера завершена");
          isAdShowing = false;
        });
    }
    // Если специальные методы недоступны, пробуем стандартные методы
    else {
      // Если найдены другие методы, связанные с interstitial, пробуем их
      if (interstitialMethods.length > 0) {
        const methodName = interstitialMethods[0];
        console.log(`[RichAds] Пробуем найденный метод ${methodName} для показа полноэкранного баннера`);

        try {
          // Пробуем вызвать найденный метод
          adsController[methodName]()
            .then((result) => {
              console.log(`[RichAds] Успешный показ полноэкранного баннера через ${methodName}:`, result);
              showStatus("Полноэкранный баннер просмотрен! Начисляем награду...", "info");
              return recordAdView('interstitial_banner_view');
            })
            .then((success) => {
              if (success) {
                tg.showPopup({
                  title: "Успех!",
                  message: "Награда за просмотр полноэкранного баннера зачислена!",
                  buttons: [{ type: "ok", text: "Отлично" }]
                });
              }
            })
            .catch((error) => {
              console.warn(`[RichAds] Ошибка показа полноэкранного баннера через ${methodName}:`, error);
              tryAlternativeMethods(error);
            })
            .finally(() => {
              if (openLinkButton) openLinkButton.disabled = false;
              console.log(`[RichAds] Операция показа полноэкранного баннера через ${methodName} завершена`);
              isAdShowing = false;
            });
        } catch (error) {
          console.warn(`[RichAds] Ошибка при вызове метода ${methodName}:`, error);
          tryAlternativeMethods(error);
        }
      } else {
        // Если не найдены специальные методы, используем стандартные
        tryAlternativeMethods();
      }
    }
  } catch (error) {
    console.error("[RichAds] Критическая ошибка при вызове полноэкранного баннера:", error);
    showStatus(`Ошибка показа баннера: ${error.message}`, "error");
    tg.showAlert(`Ошибка показа баннера: ${error.message}`);
    if (openLinkButton) openLinkButton.disabled = false;
    isAdShowing = false;
  }

  // Функция для попытки использования альтернативных методов
  function tryAlternativeMethods(originalError) {
    // Проверяем, доступен ли метод showInterstitialAd
    if (typeof adsController.showInterstitialAd === 'function') {
      console.log("[RichAds] Пробуем альтернативный метод showInterstitialAd()");

      // Вызываем метод для показа полноэкранного баннера
      adsController.showInterstitialAd()
        .then((result) => {
          console.log("[RichAds] Успешный показ полноэкранного баннера (альтернативный метод):", result);
          showStatus("Полноэкранный баннер просмотрен! Начисляем награду...", "info");
          return recordAdView('interstitial_banner_view');
        })
        .then((success) => {
          if (success) {
            startCountdown(openLinkButton);
            tg.showPopup({
              title: "Успех!",
              message: "Награда за просмотр полноэкранного баннера зачислена!",
              buttons: [{ type: "ok", text: "Отлично" }]
            });
          } else {
            startCountdown(openLinkButton);
          }
        })
        .catch((error) => {
          console.warn("[RichAds] Ошибка показа полноэкранного баннера (альтернативный метод):", error);

          // Проверяем, есть ли реклама
          if (error.message && error.message.toLowerCase().includes('no ad')) {
            showNoAdMessage();
            startCountdown(openLinkButton);
          } else {
            tryNextMethod(error);
          }
        })
        .finally(() => {
          if (openLinkButton) openLinkButton.disabled = false;
          console.log("[RichAds] Операция показа полноэкранного баннера (альтернативный метод) завершена");
          isAdShowing = false;
        });
    } else {
      tryNextMethod(originalError);
    }

    // Вложенная функция для перехода к следующему методу
    function tryNextMethod(currentError) {
      // Проверяем, доступен ли метод triggerInterstitialBanner
      if (typeof adsController.triggerInterstitialBanner === 'function') {
        console.log("[RichAds] Пробуем альтернативный метод triggerInterstitialBanner(false) для показа полноэкранного баннера");

        // Вызываем метод с параметром false для показа баннера без автоперехода
        adsController.triggerInterstitialBanner(false)
          .then((result) => {
            console.log("[RichAds] Успешный показ полноэкранного баннера (альтернативный метод):", result);
            showStatus("Полноэкранный баннер просмотрен! Начисляем награду...", "info");
            return recordAdView('interstitial_banner_view');
          })
          .then((success) => {
            if (success) {
              tg.showPopup({
                title: "Успех!",
                message: "Награда за просмотр полноэкранного баннера зачислена!",
                buttons: [{ type: "ok", text: "Отлично" }]
              });
            }
          })
          .catch((error) => {
            console.warn("[RichAds] Ошибка показа полноэкранного баннера (альтернативный метод):", error);

            // Пробуем третий альтернативный метод при ошибке
            tryFinalMethod(error);
          })
          .finally(() => {
            if (openLinkButton) openLinkButton.disabled = false;
            console.log("[RichAds] Операция показа полноэкранного баннера (альтернативный метод) завершена");
            isAdShowing = false;
          });
      } else {
        tryFinalMethod(currentError);
      }
    }

    // Вложенная функция для последней попытки
    function tryFinalMethod(currentError) {
      // Если triggerInterstitialBanner недоступен, пробуем triggerNativeNotification
      if (typeof adsController.triggerNativeNotification === 'function') {
        console.log("[RichAds] Пробуем последний альтернативный метод triggerNativeNotification(false)");

        adsController.triggerNativeNotification(false)
          .then((result) => {
            console.log("[RichAds] Успешный показ полноэкранного баннера (последний альтернативный метод):", result);
            showStatus("Полноэкранный баннер просмотрен! Начисляем награду...", "info");
            return recordAdView('interstitial_banner_view');
          })
          .then((success) => {
            if (success) {
              tg.showPopup({
                title: "Успех!",
                message: "Награда за просмотр полноэкранного баннера зачислена!",
                buttons: [{ type: "ok", text: "Отлично" }]
              });
            }
          })
          .catch((error) => {
            handleBannerError(originalError || currentError || error);
          })
          .finally(() => {
            if (openLinkButton) openLinkButton.disabled = false;
            console.log("[RichAds] Операция показа полноэкранного баннера (последний альтернативный метод) завершена");
            isAdShowing = false;
          });
      } else {
        // Если все методы недоступны, выдаем ошибку
        handleBannerError(originalError || currentError || new Error("Методы показа полноэкранного баннера недоступны"));
        isAdShowing = false;
      }
    }
  }

  // Функция для обработки ошибок полноэкранного баннера
  function handleBannerError(error) {
    console.warn("[RichAds] Ошибка показа полноэкранного баннера:", error);
    let reason = "Неизвестная ошибка";
    let userFriendlyMessage = "В данный момент полноэкранная реклама недоступна";

    if (error instanceof Error) {
      reason = error.message;

      // Проверяем, есть ли реклама
      if (reason && reason.toLowerCase().includes('no ad')) {
        showNoAdMessage();
        startCountdown(openLinkButton);
        return;
      }

      // Обработка типичных ошибок с более понятными сообщениями
      if (reason.includes("Cannot read properties of null") ||
          reason.includes("undefined") ||
          reason.includes("length")) {
        userFriendlyMessage = "В данный момент полноэкранная реклама недоступна";
      } else if (reason.includes("timeout") || reason.includes("time out")) {
        userFriendlyMessage = "Превышено время ожидания ответа от рекламной сети";
      } else if (reason.includes("network") || reason.includes("connection")) {
        userFriendlyMessage = "Проблема с сетевым подключением";
      } else {
        userFriendlyMessage = "Не удалось показать рекламу. Попробуйте позже";
      }
    } else if (typeof error === 'string' && error.trim()) {
      reason = error.trim();

      // Проверяем, есть ли реклама
      if (reason.toLowerCase().includes('no ad')) {
        showNoAdMessage();
        startCountdown(openLinkButton);
        return;
      }

      userFriendlyMessage = "Не удалось показать рекламу. Попробуйте позже";
    } else if (error && typeof error === 'object') {
      try {
        reason = JSON.stringify(error);
        userFriendlyMessage = "В данный момент полноэкранная реклама недоступна";
      } catch (e) {
        reason = "Полноэкранный баннер недоступен или был закрыт";
        userFriendlyMessage = "В данный момент полноэкранная реклама недоступна";
      }
    }

    // Записываем техническую ошибку в консоль
    console.warn(`[RichAds] Техническая причина ошибки: ${reason}`);

    // Показываем пользователю понятное сообщение
    showStatus(userFriendlyMessage, "error");
    tg.showAlert(userFriendlyMessage);
    tg.HapticFeedback.notificationOccurred("warning");

    // Запускаем счетчик даже при ошибке
    startCountdown(openLinkButton);
  }
}

// --- Функции для Секции "Заработок" ---

/** Обновляет данные в секции вывода */
function updateWithdrawalSection() {
  if (earnBalanceAmountEl) earnBalanceAmountEl.textContent = currentUserBalance;
  if (availableWithdrawalEl)
    availableWithdrawalEl.textContent = currentUserBalance;
  if (withdrawalErrorEl) withdrawalErrorEl.style.display = "none";
  if (withdrawalAmountInput) withdrawalAmountInput.value = "";
  if (withdrawalAddressInput) withdrawalAddressInput.value = "";
  if (requestWithdrawalButton) requestWithdrawalButton.disabled = true;

  // Обновляем placeholder для адреса кошелька
  updateAddressPlaceholder();

  // Загружаем историю выплат
  loadWithdrawalHistory();
}

/** Обновляет placeholder для поля адреса кошелька в зависимости от выбранной криптовалюты */
function updateAddressPlaceholder() {
  if (!cryptoCurrencySelect || !withdrawalAddressInput) return;

  const selectedCurrency = cryptoCurrencySelect.value;

  // Устанавливаем соответствующий placeholder в зависимости от выбранной валюты
  switch (selectedCurrency) {
    case 'usdttrc20':
      withdrawalAddressInput.placeholder = "Введите адрес TRC20-кошелька USDT (например: TXYZ...)";
      break;
    case 'btc':
      withdrawalAddressInput.placeholder = "Введите адрес Bitcoin-кошелька (например: 1XYZ...)";
      break;
    case 'eth':
      withdrawalAddressInput.placeholder = "Введите адрес Ethereum-кошелька (например: 0xXYZ...)";
      break;
    case 'trx':
      withdrawalAddressInput.placeholder = "Введите адрес TRON-кошелька (например: TXYZ...)";
      break;
    case 'ltc':
      withdrawalAddressInput.placeholder = "Введите адрес Litecoin-кошелька (например: LXYZ...)";
      break;
    case 'bch':
      withdrawalAddressInput.placeholder = "Введите адрес Bitcoin Cash-кошелька (например: qXYZ...)";
      break;
    case 'xrp':
      withdrawalAddressInput.placeholder = "Введите адрес Ripple-кошелька (например: rXYZ...)";
      break;
    case 'ada':
      withdrawalAddressInput.placeholder = "Введите адрес Cardano-кошелька (например: addr1...)";
      break;
    case 'dot':
      withdrawalAddressInput.placeholder = "Введите адрес Polkadot-кошелька (например: 1XYZ...)";
      break;
    default:
      withdrawalAddressInput.placeholder = "Введите адрес криптокошелька";
  }

  // Обновляем информацию о минимальной сумме и комиссиях
  const minAmountInfo = document.getElementById("min-amount-info");
  if (minAmountInfo) {
    const minAmounts = {
      'usdttrc20': '8.58',
      'btc': '0.000005',
      'eth': '0.001',
      'trx': '1.0',
      'ltc': '0.001',
      'bch': '0.001',
      'xrp': '1.0',
      'ada': '1.0',
      'dot': '0.1'
    };

    const currencyNames = {
      'usdttrc20': 'USDT (TRC20)',
      'btc': 'Bitcoin (BTC)',
      'eth': 'Ethereum (ETH)',
      'trx': 'TRON (TRX)',
      'ltc': 'Litecoin (LTC)',
      'bch': 'Bitcoin Cash (BCH)',
      'xrp': 'Ripple (XRP)',
      'ada': 'Cardano (ADA)',
      'dot': 'Polkadot (DOT)'
    };

    const feeInfo = {
      'usdttrc20': { fee: '1.0', percent: '12%', status: 'normal' },
      'xrp': { fee: '0.02', percent: '2%', status: 'best' },
      'trx': { fee: '0.1', percent: '10%', status: 'normal' },
      'dot': { fee: '0.01', percent: '10%', status: 'normal' },
      'ada': { fee: '0.17', percent: '17%', status: 'normal' },
      'ltc': { fee: '0.001', percent: '100%', status: 'warning' },
      'bch': { fee: '0.001', percent: '100%', status: 'warning' },
      'eth': { fee: '0.005', percent: '500%', status: 'bad' },
      'btc': { fee: '0.0005', percent: '10000%', status: 'bad' }
    };

    if (minAmounts[selectedCurrency] && currencyNames[selectedCurrency] && feeInfo[selectedCurrency]) {
      const currency = currencyNames[selectedCurrency];
      const minAmount = minAmounts[selectedCurrency];
      const fee = feeInfo[selectedCurrency];

      minAmountInfo.textContent = `Минимальная сумма для ${currency}: ${minAmount}, комиссия ${fee.percent}`;

      // Обновляем цвет в зависимости от статуса комиссии
      switch (fee.status) {
        case 'best':
          minAmountInfo.style.color = '#4CAF50'; // Зеленый
          break;
        case 'normal':
          minAmountInfo.style.color = '#2196F3'; // Синий
          break;
        case 'warning':
          minAmountInfo.style.color = '#FF9800'; // Оранжевый
          break;
        case 'bad':
          minAmountInfo.style.color = '#F44336'; // Красный
          break;
        default:
          minAmountInfo.style.color = '#666'; // Серый
      }
    }
  }

  // Проверяем валидность формы после изменения placeholder
  validateWithdrawalForm();
}

/** Валидирует форму вывода средств и активирует/деактивирует кнопку */
function validateWithdrawalForm() {
  if (!withdrawalAmountInput || !withdrawalAddressInput || !requestWithdrawalButton) return;

  const amount = parseInt(withdrawalAmountInput.value);
  const address = withdrawalAddressInput.value.trim();

  let isValid = true;
  let errorMessage = "";

  // Проверка минимального баланса для доступа к выводу
  if (currentUserBalance < minBalanceForWithdrawal) {
    isValid = false;
    errorMessage = `Для вывода средств необходимо иметь минимум ${minBalanceForWithdrawal} монет на балансе`;
  }
  // Проверка суммы (только если баланс достаточен для доступа к выводу)
  else if (!amount || isNaN(amount) || amount <= 0) {
    isValid = false;
    errorMessage = "Введите корректную сумму для вывода";
  } else if (amount > currentUserBalance) {
    isValid = false;
    errorMessage = "Недостаточно средств на балансе";
  } else if (minWithdrawalAmount > 0 && amount < minWithdrawalAmount) {
    isValid = false;
    errorMessage = `Минимальная сумма для вывода: ${minWithdrawalAmount} монет`;
  }

  // Проверка адреса кошелька
  if (!address && isValid) {
    isValid = false;
    if (!errorMessage) errorMessage = "Введите адрес кошелька";
  } else if (address.length < 20 && address.length > 0 && isValid) {
    isValid = false;
    if (!errorMessage) errorMessage = "Адрес кошелька слишком короткий";
  }

  // Обновляем состояние кнопки
  requestWithdrawalButton.disabled = !isValid;

  // Показываем/скрываем сообщение об ошибке
  if (withdrawalErrorEl) {
    if (errorMessage && !isValid && (amount > 0 || address.length > 0 || currentUserBalance < minBalanceForWithdrawal)) {
      withdrawalErrorEl.textContent = errorMessage;
      withdrawalErrorEl.style.display = "block";
    } else {
      withdrawalErrorEl.style.display = "none";
    }
  }
}



/** Загружает историю выплат пользователя */
async function loadWithdrawalHistory() {
  if (!tg.initData) {
    console.warn("Нет данных Telegram для загрузки истории выплат");
    return;
  }

  const historyContainer = document.querySelector('.placeholder-list');
  if (!historyContainer) {
    console.warn("Не найден контейнер для истории выплат");
    return;
  }

  try {
    const response = await fetch(`${API_BASE_URL}/getWithdrawalHistory.php`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ initData: tg.initData }),
    });

    if (!response.ok) {
      throw new Error(`Ошибка: ${response.status}`);
    }

    const data = await response.json();

    if (data.error) {
      throw new Error(data.error);
    }

    // Очищаем контейнер
    historyContainer.innerHTML = "";

    if (data.withdrawals && data.withdrawals.length > 0) {
      // Создаем список выплат
      data.withdrawals.forEach(withdrawal => {
        const withdrawalItem = document.createElement("div");
        withdrawalItem.className = "withdrawal-item";

        const date = new Date(withdrawal.timestamp * 1000);
        const dateStr = date.toLocaleDateString('ru-RU');
        const timeStr = date.toLocaleTimeString('ru-RU', { hour: '2-digit', minute: '2-digit' });

        // Определяем статус на русском
        let statusText = "Неизвестно";
        let statusClass = "";
        switch (withdrawal.status) {
          case 'pending':
            statusText = "В обработке";
            statusClass = "status-pending";
            break;
          case 'completed':
            statusText = "Завершено";
            statusClass = "status-completed";
            break;
          case 'failed':
            statusText = "Ошибка";
            statusClass = "status-failed";
            break;
        }

        // Добавляем кнопку отмены для платежей в обработке
        const canCancel = withdrawal.status === 'pending' || withdrawal.status === 'processing' || withdrawal.status === 'В ОБРАБОТКЕ';
        const cancelButton = canCancel ? `
          <div class="withdrawal-actions">
            <button class="cancel-button" onclick="cancelWithdrawal('${withdrawal.id}')">
              Отменить
            </button>
          </div>
        ` : '';

        withdrawalItem.innerHTML = `
          <div class="withdrawal-header">
            <span class="withdrawal-amount">${withdrawal.coins_amount} монет</span>
            <span class="withdrawal-status ${statusClass}">${statusText}</span>
          </div>
          <div class="withdrawal-details">
            <div class="withdrawal-currency">${withdrawal.currency.toUpperCase()}</div>
            <div class="withdrawal-date">${dateStr} ${timeStr}</div>
          </div>
          <div class="withdrawal-address">${withdrawal.address}</div>
          ${cancelButton}
        `;

        historyContainer.appendChild(withdrawalItem);
      });
    } else {
      historyContainer.innerHTML = "<p class='hint'>У вас пока нет заявок на вывод средств.</p>";
    }

  } catch (error) {
    console.error("Ошибка загрузки истории выплат:", error);
    historyContainer.innerHTML = "<p class='hint'>Не удалось загрузить историю выплат.</p>";
  }
}

/** Отменяет выплату по ID */
async function cancelWithdrawal(withdrawalId) {
  if (!tg.initData) {
    showStatus("Ошибка: Нет данных Telegram.", "error");
    return;
  }

  // Подтверждение отмены
  const confirmed = await new Promise((resolve) => {
    tg.showPopup({
      title: "Отмена выплаты",
      message: "Вы уверены, что хотите отменить эту выплату? Средства будут возвращены на ваш баланс.",
      buttons: [
        { type: "cancel", text: "Нет" },
        { type: "destructive", text: "Да, отменить" }
      ]
    }, (buttonId) => {
      resolve(buttonId === "destructive");
    });
  });

  if (!confirmed) return;

  try {
    showStatus("Отмена выплаты...");

    const response = await fetch(`${API_BASE_URL}/cancelWithdrawal.php`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        initData: tg.initData,
        withdrawal_id: withdrawalId
      }),
    });

    if (!response.ok) {
      let errorText = `Ошибка: ${response.status}`;
      try {
        const errorData = await response.json();
        if (errorData && errorData.error) {
          errorText = errorData.error;
        }
      } catch (e) {}
      throw new Error(errorText);
    }

    const data = await response.json();

    // Обновляем баланс
    if (data.new_balance !== undefined) {
      updateBalanceDisplay(data.new_balance);
    }

    // Показываем успех
    showStatus("Выплата отменена, средства возвращены на баланс!", "success");
    tg.showPopup({
      title: "Успех!",
      message: `Выплата отменена. ${data.returned_amount || ''} монет возвращено на ваш баланс.`,
      buttons: [{ type: "ok", text: "Понятно" }]
    });

    // Обновляем историю выплат
    loadWithdrawalHistory();

  } catch (error) {
    console.error("Ошибка отмены выплаты:", error);
    showStatus(`Ошибка отмены: ${error.message}`, "error");
    tg.showAlert(`Не удалось отменить выплату:\n${error.message}`);
  }
}

/** Обработчик для кнопки "Запросить вывод" */
async function handleRequestWithdrawal() {
  if (!tg.initData) {
    showStatus("Ошибка: Нет данных Telegram.", "error");
    return;
  }

  const amount = parseInt(withdrawalAmountInput.value);
  const address = withdrawalAddressInput.value.trim();
  const currency = cryptoCurrencySelect.value;

  // Блокируем кнопку
  requestWithdrawalButton.disabled = true;
  showStatus("Отправка запроса на вывод...");

  try {
    const response = await fetch(`${API_BASE_URL}/requestWithdrawal.php`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        initData: tg.initData,
        amount: amount,
        crypto_address: address,
        crypto_currency: currency
      }),
    });

    if (!response.ok) {
      let errorText = `Ошибка: ${response.status}`;
      try {
        const errorData = await response.json();
        if (errorData && errorData.error) {
          errorText = errorData.error;
        }
      } catch (e) {}
      throw new Error(errorText);
    }

    const data = await response.json();

    // Обновляем баланс
    updateBalanceDisplay(data.new_balance);

    // Формируем сообщение с учетом типа конвертации
    let successMessage = `Запрос на вывод ${amount} монет в ${currency.toUpperCase()} создан!`;
    let statusMessage = "Запрос на вывод создан!";

    // Добавляем информацию о комиссиях, если есть
    if (data.fee_handling) {
      successMessage += `\n\n💳 Информация о комиссии:\n${data.fee_handling.note}`;

      if (data.fee_handling.fee_estimate && data.fee_handling.fee_estimate.fee) {
        const fee = data.fee_handling.fee_estimate.fee;
        const feeCurrency = data.fee_handling.fee_estimate.currency || currency.toUpperCase();
        successMessage += `\n📊 Комиссия: ${fee} ${feeCurrency}`;
      }

      if (data.fee_handling.min_amount) {
        successMessage += `\n📏 Минимум: ${data.fee_handling.min_amount} ${currency.toUpperCase()}`;
      }
    }

    // Проверяем, была ли использована внутренняя конвертация NOWPayments
    if (data.internal_conversion) {
      const internalConv = data.internal_conversion;
      const targetCurrency = internalConv.target_currency.toUpperCase();
      const targetAmount = internalConv.target_amount;

      successMessage += `\n\n⚡ Использована внутренняя конвертация NOWPayments`;
      successMessage += `\n💰 Выплата: ${targetAmount} ${targetCurrency}`;
      statusMessage = "Выплата создана через внутреннюю конвертацию!";

      console.log("Внутренняя конвертация NOWPayments:", internalConv);
    }
    // Проверяем, была ли использована внешняя автоконвертация
    else if (data.auto_conversion) {
      const autoConv = data.auto_conversion;
      const actualCurrency = autoConv.actual_payout.currency.toUpperCase();
      const actualAmount = autoConv.actual_payout.amount;

      successMessage += `\n\n🔄 Использована внешняя автоконвертация из ${actualCurrency}`;
      successMessage += `\n💰 Выплата: ${actualAmount} ${actualCurrency}`;
      statusMessage = "Выплата создана через автоконвертацию!";

      console.log("Внешняя автоконвертация:", autoConv);
    }

    // Показываем успех
    showStatus(statusMessage, "success");
    tg.showPopup({
      title: "Успех!",
      message: successMessage,
      buttons: [{ type: "ok", text: "Отлично" }]
    });

    // Очищаем форму
    withdrawalAmountInput.value = "";
    withdrawalAddressInput.value = "";

    // Обновляем секцию вывода (включая историю)
    updateWithdrawalSection();

  } catch (error) {
    console.error("Ошибка вывода:", error);

    // Специальная обработка различных типов ошибок
    if (error.message.includes('не подходит для валюты') ||
        error.message.includes('Автоконвертация невозможна') ||
        error.message.includes('INCOMPATIBLE_ADDRESS')) {

      // Показываем детальное объяснение пользователю
      const currency = cryptoCurrencySelect.value.toUpperCase();
      const address = withdrawalAddressInput.value.trim();

      let helpMessage = `Ваш адрес не подходит для автоконвертации в ${currency}.\n\n`;
      helpMessage += `💡 Решения:\n`;
      helpMessage += `1. Укажите адрес, совместимый с ${currency}\n`;
      helpMessage += `2. Выберите валюту, совместимую с вашим адресом\n`;
      helpMessage += `3. Обратитесь в поддержку за помощью`;

      showStatus("Адрес несовместим с выбранной валютой", "error");

      tg.showPopup({
        title: "Несовместимый адрес",
        message: helpMessage,
        buttons: [{ type: "ok", text: "Понятно" }]
      });

    } else if (error.message.includes('нужен баланс USDT TRC20') ||
               error.message.includes('NEED_TRON_BALANCE')) {

      // Специальная обработка для TRON адресов
      let helpMessage = `Для выплаты на TRON кошелек требуется баланс USDT TRC20 или TRX.\n\n`;
      helpMessage += `💡 Решения:\n`;
      helpMessage += `1. Пополните баланс USDT TRC20 в панели NOWPayments\n`;
      helpMessage += `2. Используйте BTC адрес для автоконвертации\n`;
      helpMessage += `3. Обратитесь к администратору для пополнения баланса`;

      showStatus("Нужен баланс USDT TRC20 для TRON адресов", "error");

      tg.showPopup({
        title: "Требуется пополнение баланса",
        message: helpMessage,
        buttons: [{ type: "ok", text: "Понятно" }]
      });

    } else if (error.message.includes('Минимальная сумма для выплаты')) {

      // Специальная обработка для ошибок минимальной суммы
      let helpMessage = error.message;

      // Пытаемся извлечь детали из ошибки
      const minAmountMatch = error.message.match(/Минимальная сумма для выплаты (\w+): ([\d.]+)/);
      if (minAmountMatch) {
        const currency = minAmountMatch[1];
        const minAmount = minAmountMatch[2];

        helpMessage += `\n\n💡 Решения:\n`;
        helpMessage += `1. Увеличьте сумму до ${minAmount} ${currency} или больше\n`;
        helpMessage += `2. Выберите другую криптовалюту с меньшим минимумом\n`;
        helpMessage += `3. Накопите больше монет для вывода`;
      }

      showStatus("Сумма меньше минимальной", "error");

      tg.showPopup({
        title: "Сумма слишком мала",
        message: helpMessage,
        buttons: [{ type: "ok", text: "Понятно" }]
      });

    } else {
      // Обычная обработка других ошибок
      showStatus(`Ошибка: ${error.message}`, "error");
    }

    // Показываем ошибку в форме
    if (withdrawalErrorEl) {
      withdrawalErrorEl.textContent = error.message;
      withdrawalErrorEl.style.display = "block";
    }

    // Разблокируем кнопку
    requestWithdrawalButton.disabled = false;
  }
}

// --- Функции для Секции "Друзья" ---

/** Генерирует и отображает реферальную ссылку пользователя. */
function generateReferralLink() {
  if (currentUserId && referralLinkInput) {
    const link = `https://t.me/${BOT_USERNAME}?start=${currentUserId}`;
    referralLinkInput.value = link;
    if (copyReferralButton) copyReferralButton.disabled = false;
  } else if (referralLinkInput) {
    referralLinkInput.value = "Загрузка...";
    if (copyReferralButton) copyReferralButton.disabled = true;
  }
}

/** Копирует реферальную ссылку в буфер обмена (с Fallback) */
function copyReferralLink() {
  if (!referralLinkInput) {
    tg.showAlert("Ошибка: Не найден элемент ссылки.");
    return;
  }
  const textToCopy = referralLinkInput.value;
  if (navigator.clipboard && window.isSecureContext) {
    console.log("Копирование через navigator.clipboard...");
    navigator.clipboard
      .writeText(textToCopy)
      .then(() => {
        console.log("Успех clipboard.");
        showSuccessCopyFeedback();
      })
      .catch((err) => {
        console.warn("Ошибка navigator.clipboard:", err);
        tryFallbackCopy(textToCopy);
      });
  } else {
    console.log("navigator.clipboard недоступен, пробуем fallback.");
    tryFallbackCopy(textToCopy);
  }
}

/** Загружает и отображает статистику рефералов */
async function loadReferralStats() {
  if (!tg.initData) {
    showStatus("Ошибка: Нет данных Telegram.", "error");
    return;
  }

  const referralsCountEl = document.getElementById("referrals-count");
  const referralEarningsEl = document.getElementById("referral-earnings");
  const referralsListEl = document.getElementById("referrals-list");
  const refreshStatsButton = document.getElementById("refresh-stats-button");

  if (!referralsCountEl || !referralEarningsEl || !referralsListEl) {
    console.error("Не найдены элементы для отображения статистики рефералов");
    return;
  }

  // Отключаем кнопку обновления на время загрузки
  if (refreshStatsButton) refreshStatsButton.disabled = true;

  showStatus("Загрузка статистики рефералов...");

  try {
    const response = await fetch(`${API_BASE_URL}/getReferralStats.php`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ initData: tg.initData }),
    });

    if (!response.ok) {
      let eText = `Ошибка: ${response.status}`;
      try {
        const eData = await response.json();
        if (eData && eData.error) eText = `Ошибка: ${eData.error}`;
      } catch (e) {}
      throw new Error(eText);
    }

    const data = await response.json();

    // Обновляем счетчики
    referralsCountEl.textContent = data.referralsCount;
    referralEarningsEl.textContent = data.totalEarned;

    // Обновляем информацию о реферере в разделе "Подписки"
    const subscriptionsListEl = document.getElementById("subscriptions-list");
    if (subscriptionsListEl) {
      if (data.referrerInfo) {
        subscriptionsListEl.innerHTML = `
          <div class="referral-item">
            <div class="referral-name">Вас пригласил: ${data.referrerInfo.display_name}</div>
          </div>
        `;
      } else {
        subscriptionsListEl.innerHTML = "<p class='hint'>Вы не были приглашены другим пользователем.</p>";
      }
    }

    // Обновляем список рефералов
    if (data.referrals && data.referrals.length > 0) {
      // Очищаем список
      referralsListEl.innerHTML = "";

      // Добавляем каждого реферала в список
      data.referrals.forEach(referral => {
        const referralItem = document.createElement("div");
        referralItem.className = "referral-item";

        const referralName = document.createElement("div");
        referralName.className = "referral-name";

        // Используем display_name, если он есть, иначе показываем ID
        if (referral.display_name) {
          referralName.textContent = referral.display_name;
        } else {
          referralName.textContent = `ID: ${referral.id}`;
        }

        const referralInfo = document.createElement("div");
        referralInfo.className = "referral-info";

        // Форматируем дату
        const joinDate = new Date(referral.joined * 1000);
        const dateStr = joinDate.toLocaleDateString();

        referralInfo.textContent = `Баланс: ${referral.balance} • Присоединился: ${dateStr}`;

        referralItem.appendChild(referralName);
        referralItem.appendChild(referralInfo);

        referralsListEl.appendChild(referralItem);
      });
    } else {
      referralsListEl.innerHTML = "<p class='hint'>У вас пока нет рефералов. Пригласите друзей!</p>";
    }

    showStatus("Статистика рефералов обновлена", "success");
    setTimeout(() => {
      if (statusMessageEl.textContent === "Статистика рефералов обновлена") showStatus("");
    }, 2000);
  } catch (error) {
    console.error("[Referral Stats] Ошибка:", error);
    showStatus(`Ошибка загрузки статистики: ${error.message}`, "error");
    referralsListEl.innerHTML = "<p class='hint'>Не удалось загрузить данные о рефералах.</p>";
  } finally {
    // Включаем кнопку обновления
    if (refreshStatsButton) refreshStatsButton.disabled = false;
  }
}

/** Регистрирует пользователя как реферала, если он перешел по реферальной ссылке */
async function registerAsReferral() {
  // Проверяем, есть ли параметр start в URL
  const urlParams = new URLSearchParams(window.location.search);
  const startParam = urlParams.get('start');

  if (!startParam || !tg.initData) {
    // Нет параметра start или данных Telegram, значит это не реферальный переход
    return;
  }

  console.log(`[Referral] Обнаружен параметр start: ${startParam}`);
  showStatus("Регистрация реферала...");

  try {
    const response = await fetch(`${API_BASE_URL}/registerReferral.php`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        initData: tg.initData,
        referrerId: startParam
      }),
    });

    if (!response.ok) {
      let eText = `Ошибка: ${response.status}`;
      try {
        const eData = await response.json();
        if (eData && eData.error) eText = `Ошибка: ${eData.error}`;
      } catch (e) {}
      throw new Error(eText);
    }

    const data = await response.json();

    if (data.success) {
      showStatus("Вы успешно зарегистрированы как реферал!", "success");
      tg.showPopup({
        title: "Успех!",
        message: "Вы успешно зарегистрированы как реферал!",
        buttons: [{ type: "ok", text: "Отлично" }]
      });
    } else {
      console.log(`[Referral] Регистрация не требуется: ${data.message}`);
    }
  } catch (error) {
    console.error("[Referral Registration] Ошибка:", error);
    // Не показываем ошибку пользователю, чтобы не мешать основному использованию приложения
  } finally {
    showStatus("");
  }
}
/** Fallback функция для копирования через document.execCommand */
function tryFallbackCopy(textToCopy) {
  console.log("Копирование через document.execCommand...");
  try {
    const textArea = document.createElement("textarea");
    textArea.value = textToCopy;
    textArea.style.position = "absolute";
    textArea.style.left = "-9999px";
    document.body.appendChild(textArea);
    textArea.select();
    const successful = document.execCommand("copy");
    document.body.removeChild(textArea);
    if (successful) {
      console.log("Успех execCommand.");
      showSuccessCopyFeedback();
    } else {
      throw new Error("execCommand вернул false");
    }
  } catch (err) {
    console.error("Ошибка document.execCommand:", err);
    tg.showAlert(
      "Не удалось скопировать ссылку. Выделите и скопируйте вручную."
    );
    if (referralLinkInput) {
      try {
        referralLinkInput.focus();
        referralLinkInput.select();
        referralLinkInput.setSelectionRange(0, 99999);
      } catch (e) {}
    }
    tg.HapticFeedback.notificationOccurred("error");
  }
}
/** Показывает визуальную обратную связь при успешном копировании */
function showSuccessCopyFeedback() {
  tg.HapticFeedback.notificationOccurred("success");
  showStatus("Ссылка скопирована!", "success");
  if (copyReferralButton) {
    const t = copyReferralButton.textContent;
    copyReferralButton.textContent = "✅";
    setTimeout(() => {
      copyReferralButton.textContent = t;
      if (statusMessageEl.textContent === "Ссылка скопирована!") showStatus("");
    }, 1500);
  }
}
/** Обработчик нажатия на кнопку "Поделиться приложением" */
function handleShareAppClick() {
  console.log("Попытка поделиться...");
  tg.HapticFeedback.impactOccurred("light");
  const shareUrl = `https://t.me/${BOT_USERNAME}`;
  const shareText = `Привет! Зацени крутое приложение для заработка: ${shareUrl}`;
  try {
    console.log(`Попытка tg.shareApp`);
    tg.shareApp({ url: shareUrl, text: shareText });
    console.log("shareApp вызван.");
    return;
  } catch (shareError) {
    console.warn("Ошибка tg.shareApp(options):", shareError);
  }
  try {
    const telegramShareUrl = `https://t.me/share/url?url=${encodeURIComponent(
      shareUrl
    )}&text=${encodeURIComponent(shareText)}`;
    console.log(`Попытка tg.openTelegramLink: ${telegramShareUrl}`);
    tg.openTelegramLink(telegramShareUrl);
    console.log("openTelegramLink вызван.");
  } catch (linkError) {
    console.error("Ошибка tg.openTelegramLink:", linkError);
    tg.showAlert("Не удалось открыть окно 'Поделиться'.");
  }
}

// --- Инициализация Приложения ---

/** Основная функция инициализации Mini App. */
function initializeApp() {
  console.log(`Инициализация ${BOT_USERNAME} App (v с анимацией v6)...`);
  tg.ready();
  tg.expand();

  const requiredElements = [
    mainContentEl,
    earnSectionEl,
    friendsSectionEl,
    userNameEl,
    balanceAmountEl,
    headerBalanceInfoEl,
    watchAdButton,
    watchVideoButton,
    openLinkButton,
    statusMessageEl,
    shareAppButton,
    referralLinkInput,
    copyReferralButton,
    navHomeButton,
    navEarnButton,
    navFriendsButton,
    earnBalanceAmountEl,
    availableWithdrawalEl,
    minWithdrawalEl,
    withdrawalAmountInput,
    withdrawalAddressInput,
    cryptoCurrencySelect,
    requestWithdrawalButton,
    withdrawalErrorEl,
  ];
  const missingElement = requiredElements.find((el) => !el);
  if (missingElement) {
    console.error(
      "КРИТИЧЕСКАЯ ОШИБКА: Не найден элемент интерфейса!",
      missingElement
    );
    showStatus("Ошибка загрузки интерфейса!", "error");
    tg.showAlert("Ошибка интерфейса приложения.");
    return;
  }

  // Устанавливаем начальное состояние интерфейса
  allPages.forEach((page) => {
    if (page && page !== mainContentEl) {
      page.classList.add("page-hidden");
      page.style.display = ""; /* Сбрасываем инлайн стиль при инициализации */
    }
  });

  // 🔄 ВОССТАНОВЛЕНИЕ АКТИВНОЙ СТРАНИЦЫ после reload
  const lastActivePage = localStorage.getItem('lastActivePage');
  let initialPage = mainContentEl;
  let initialNavButton = navHomeButton;

  if (lastActivePage) {
    console.log(`[Page Restore] Восстанавливаем последнюю активную страницу: ${lastActivePage}`);

    // Определяем какую страницу восстановить
    switch (lastActivePage) {
      case 'earn-section':
        initialPage = earnSectionEl;
        initialNavButton = navEarnButton;
        break;
      case 'friends-section':
        initialPage = friendsSectionEl;
        initialNavButton = navFriendsButton;
        break;
      default:
        initialPage = mainContentEl;
        initialNavButton = navHomeButton;
    }

    // Очищаем сохраненную страницу
    localStorage.removeItem('lastActivePage');
  }

  // Устанавливаем активную страницу
  allPages.forEach((page) => {
    if (page === initialPage) {
      page.classList.remove("page-hidden");
      page.style.display = "";
    } else {
      page.classList.add("page-hidden");
    }
  });

  currentPageElement = initialPage;
  updateActiveNavButton(initialNavButton);

  console.log(`[Page Restore] Активная страница установлена: ${currentPageElement.id}`);

  watchAdButton.disabled = true;
  watchVideoButton.disabled = true;
  openLinkButton.disabled = true;
  copyReferralButton.disabled = true;
  requestWithdrawalButton.disabled = true;

  // Назначение обработчиков событий
  navHomeButton.addEventListener("click", showMainContent);
  navEarnButton.addEventListener("click", showEarnSection);
  navFriendsButton.addEventListener("click", showFriendsSection);
  headerBalanceInfoEl.addEventListener("click", showEarnSection);
  watchAdButton.addEventListener("click", handleWatchAdClick);
  watchVideoButton.addEventListener("click", handleWatchVideoClick);
  openLinkButton.addEventListener("click", handleOpenLinkClick);
  shareAppButton.addEventListener("click", handleShareAppClick);
  copyReferralButton.addEventListener("click", copyReferralLink);
  requestWithdrawalButton.addEventListener("click", handleRequestWithdrawal);

  // Инициализация формы вывода средств
  if (cryptoCurrencySelect) {
    cryptoCurrencySelect.addEventListener("change", updateAddressPlaceholder);
  }

  // Добавляем обработчики для валидации формы в реальном времени
  if (withdrawalAmountInput) {
    withdrawalAmountInput.addEventListener("input", () => {
      validateWithdrawalForm();
      updateCryptoAmountField();
    });
  }

  if (withdrawalAddressInput) {
    withdrawalAddressInput.addEventListener("input", validateWithdrawalForm);
  }

  // Добавляем обработчик для изменения криптовалюты
  if (cryptoCurrencySelect) {
    cryptoCurrencySelect.addEventListener("change", updateCryptoAmountField);
  }

  // Добавляем обработчик для кнопки обновления статистики рефералов
  const refreshStatsButton = document.getElementById("refresh-stats-button");
  if (refreshStatsButton) {
    refreshStatsButton.addEventListener("click", loadReferralStats);
  }

  // Запуск асинхронных операций
  showStatus("Загрузка данных...", "info");

  console.log("Инициализация приложения начата...");

  // Проверяем, есть ли параметр start в URL для регистрации реферала
  registerAsReferral();

  // Инициализируем локализацию (неблокирующая)
  if (window.appLocalization) {
    console.log("Локализация инициализирована");

    // Принудительно применяем переводы, если они уже загружены
    if (window.appLocalization.isLoaded) {
      window.appLocalization.applyTranslations();
      console.log("Переводы применены при инициализации");
    }
  }

  fetchUserData()
    .then(() => {
      console.log("Загрузка данных пользователя завершена.");

      // Загружаем статистику рефералов, если мы на странице друзей
      if (currentPageElement === friendsSectionEl) {
        loadReferralStats();
      }

      if (typeof TelegramAdsController === "function") {
        initializeRichAds();
      } else {
        console.warn("Ожидание RichAds SDK...");
        showStatus("Ожидание модуля рекламы...", "info");
        let attempts = 0;
        const maxAttempts = 50;
        const interval = 100;
        const checkInterval = setInterval(() => {
          attempts++;
          if (typeof TelegramAdsController === "function") {
            clearInterval(checkInterval);
            initializeRichAds();
          } else if (attempts >= maxAttempts) {
            clearInterval(checkInterval);
            console.error("Тайм-аут RichAds SDK");
            showStatus("Ошибка: Модуль рекламы не загружен.", "error");
            tg.showAlert("Не удалось загрузить модуль рекламы.");

            // Блокируем все кнопки рекламы при тайм-ауте
            if (watchAdButton) watchAdButton.disabled = true;
            if (watchVideoButton) watchVideoButton.disabled = true;
            if (openLinkButton) openLinkButton.disabled = true;
          }
        }, interval);
      }
    })
    .catch((err) => {
      console.error("Ошибка fetchUserData:", err);
      showStatus("Ошибка загрузки данных.", "error");
      tg.showAlert("Ошибка загрузки данных.");

      // Блокируем все кнопки рекламы при ошибке загрузки данных
      if (watchAdButton) watchAdButton.disabled = true;
      if (watchVideoButton) watchVideoButton.disabled = true;
      if (openLinkButton) openLinkButton.disabled = true;
    });
  console.log("Инициализация настроена.");
}

// --- Точка Входа ---
if (document.readyState === "loading") {
  document.addEventListener("DOMContentLoaded", function() {
    initializeApp();
    initializeWithdrawalCalculator();
  });
} else {
  initializeApp();
  initializeWithdrawalCalculator();
}

// === КАЛЬКУЛЯТОР ВЫВОДА ===

/**
 * Данные о валютах с актуальными комиссиями
 */
const currencyData = {
  eth: {
    name: 'Ethereum (ETH)',
    minCoins: 1000, // 1000 монет = $1.0
    networkFee: 0.53, // Комиссия $0.53
    status: 'best'
  },
  btc: {
    name: 'Bitcoin (BTC)',
    minCoins: 5, // 5 монет = $0.005
    networkFee: 1.48, // Комиссия $1.48
    status: 'good'
  },
  usdttrc20: {
    name: 'USDT (TRC20)',
    minCoins: 8580, // 8580 монет = $8.58
    networkFee: 5.58, // Средняя комиссия $5.58
    status: 'expensive'
  },
  trx: {
    name: 'TRON (TRX)',
    minCoins: 1000, // 1000 монет = $1.0
    networkFee: 5.58, // Средняя комиссия $5.58
    status: 'expensive'
  }
};

/**
 * Инициализирует калькулятор вывода
 */
function initializeWithdrawalCalculator() {
  const calcAmountInput = document.getElementById('calc-amount');
  const calcBalance = document.getElementById('calc-balance');
  const currencyTabs = document.querySelectorAll('.currency-tab');

  if (!calcAmountInput) return;

  // Инициализируем табы валют
  initializeCurrencyTabs();

  // Обновляем баланс в калькуляторе
  if (calcBalance) {
    calcBalance.textContent = `${currentUserBalance || 0} монет`;
  }

  // Обработчик изменения суммы
  calcAmountInput.addEventListener('input', function() {
    const amount = parseInt(this.value) || 0;
    updateCalculatorDisplay(amount);
    updateBalanceCheck(amount);
  });

  // Обработчики для выбора валюты
  currencyTabs.forEach(tab => {
    tab.addEventListener('click', function() {
      const currency = this.dataset.currency;
      if (!this.classList.contains('insufficient')) {
        selectCurrencyTab(currency);
      }
    });
  });

  // Первоначальный расчет для ETH (активная по умолчанию)
  updateCalculatorDisplay(0);
}

/**
 * Инициализирует табы валют
 */
function initializeCurrencyTabs() {
  const currencyTabs = document.querySelectorAll('.currency-tab');

  currencyTabs.forEach(tab => {
    tab.addEventListener('click', function() {
      const currency = this.dataset.currency;
      if (!this.classList.contains('insufficient')) {
        selectCurrencyTab(currency);
      }
    });
  });
}

/**
 * Выбирает таб валюты
 */
function selectCurrencyTab(currency) {
  const currencyTabs = document.querySelectorAll('.currency-tab');

  // Убираем активный класс со всех табов
  currencyTabs.forEach(tab => {
    tab.classList.remove('active');
  });

  // Добавляем активный класс к выбранному табу
  const selectedTab = document.querySelector(`[data-currency="${currency}"]`);
  if (selectedTab) {
    selectedTab.classList.add('active');
  }

  // Обновляем информацию о валюте
  updateCurrencyInfo(currency);

  // Пересчитываем с текущей суммой
  const calcAmountInput = document.getElementById('calc-amount');
  const amount = parseInt(calcAmountInput?.value) || 0;
  updateCalculatorDisplay(amount);

  // Обновляем форму вывода
  updateWithdrawalForm(currency, amount);
}

/**
 * Обновляет форму вывода с выбранной валютой
 */
function updateWithdrawalForm(currency, amount) {
  const withdrawalAmountInput = document.getElementById('withdrawal-amount');
  const cryptoCurrencySelect = document.getElementById('crypto-currency');

  if (withdrawalAmountInput) {
    withdrawalAmountInput.value = amount;
  }

  if (cryptoCurrencySelect) {
    cryptoCurrencySelect.value = currency;
  }

  // Обновляем расчет суммы к получению
  updateCryptoAmountField();

  // Обновляем placeholder адреса
  updateAddressPlaceholder();

  // Валидируем форму
  validateWithdrawalForm();
}

/**
 * Обновляет информацию о выбранной валюте
 */
function updateCurrencyInfo(currency) {
  const data = currencyData[currency];
  if (!data) return;

  // Обновляем иконку и название
  const currencyIcon = document.querySelector('.currency-icon');
  const currencyFullName = document.querySelector('.currency-full-name');
  const currencyBadge = document.querySelector('.currency-badge');

  if (currencyIcon) {
    const icons = {
      'eth': '⭐',
      'btc': '₿',
      'usdttrc20': '💲',
      'trx': '🔺'
    };
    currencyIcon.textContent = icons[currency] || '💰';
  }

  if (currencyFullName) {
    currencyFullName.textContent = data.name;
  }

  if (currencyBadge) {
    currencyBadge.className = `currency-badge status-${data.status}`;
    const statusTexts = {
      'best': window.appLocalization ? window.appLocalization.get('earnings.best_choice') : 'Лучший выбор',
      'good': window.appLocalization ? window.appLocalization.get('earnings.good') : 'Хорошо',
      'expensive': window.appLocalization ? window.appLocalization.get('earnings.expensive') : 'Дорого'
    };
    currencyBadge.textContent = statusTexts[data.status] || 'Обычно';
  }

  // Обновляем требования
  const requirementValues = document.querySelectorAll('.requirement-value');
  if (requirementValues.length >= 2) {
    requirementValues[0].textContent = `${data.minCoins.toLocaleString()} монет ($${(data.minCoins * 0.001).toFixed(2)})`;
    requirementValues[1].textContent = `$${data.networkFee}`;
  }
}

/**
 * Обновляет отображение калькулятора для выбранной валюты
 */
function updateCalculatorDisplay(coinAmount) {
  const dollarAmount = coinAmount * 0.001; // 1 монета = $0.001
  const dollarEquivalent = document.getElementById('dollar-equivalent');

  if (dollarEquivalent) {
    dollarEquivalent.textContent = `= $${dollarAmount.toFixed(3)}`;
  }

  // Получаем активную валюту
  const activeTab = document.querySelector('.currency-tab.active');
  if (!activeTab) return;

  const currency = activeTab.dataset.currency;
  const data = currencyData[currency];
  if (!data) return;

  // Обновляем расчеты для выбранной валюты
  updateCurrencyCalculationDisplay(currency, coinAmount, dollarAmount);

  // Обновляем статус табов (доступность)
  updateCurrencyTabsStatus(coinAmount);
}

/**
 * Обновляет расчеты для конкретной валюты в новом интерфейсе
 */
function updateCurrencyCalculationDisplay(currency, coinAmount, dollarAmount) {
  const data = currencyData[currency];
  if (!data) return;

  const withdrawalAmountDisplay = document.getElementById('withdrawal-amount-display');
  const feeAmountDisplay = document.getElementById('fee-amount-display');
  const finalAmountDisplay = document.getElementById('final-amount-display');
  const efficiencyDisplay = document.getElementById('efficiency-display');
  const actionStatus = document.getElementById('action-status');

  const userBalance = currentUserBalance || 0;

  // Проверяем различные условия
  if (coinAmount === 0) {
    // Нет суммы
    if (withdrawalAmountDisplay) withdrawalAmountDisplay.textContent = '-';
    if (feeAmountDisplay) feeAmountDisplay.textContent = '-';
    if (finalAmountDisplay) finalAmountDisplay.textContent = '-';
    if (efficiencyDisplay) efficiencyDisplay.textContent = '-';

    if (actionStatus) {
      actionStatus.className = 'action-status-card neutral';
      actionStatus.querySelector('.status-icon').textContent = '💡';
      actionStatus.querySelector('.status-text').textContent = window.appLocalization ?
        window.appLocalization.get('earnings.enter_coins_amount') : 'Введите сумму для расчета';
    }

  } else if (coinAmount > userBalance) {
    // Недостаточно средств на балансе
    if (withdrawalAmountDisplay) withdrawalAmountDisplay.textContent = `$${dollarAmount.toFixed(3)}`;
    if (feeAmountDisplay) feeAmountDisplay.textContent = `$${data.networkFee}`;
    if (finalAmountDisplay) finalAmountDisplay.textContent = 'Недостаточно средств';
    if (efficiencyDisplay) efficiencyDisplay.textContent = '-';

    if (actionStatus) {
      actionStatus.className = 'action-status-card insufficient-funds';
      actionStatus.querySelector('.status-icon').textContent = '❌';
      const needAmount = coinAmount - userBalance;
      actionStatus.querySelector('.status-text').textContent = window.appLocalization ?
        window.appLocalization.get('earnings.insufficient_funds_need', {amount: needAmount}) :
        `Недостаточно средств! Нужно: ${needAmount} монет`;
    }

  } else if (coinAmount < data.minCoins) {
    // Меньше минимума
    if (withdrawalAmountDisplay) withdrawalAmountDisplay.textContent = `$${dollarAmount.toFixed(3)}`;
    if (feeAmountDisplay) feeAmountDisplay.textContent = `$${data.networkFee}`;
    if (finalAmountDisplay) finalAmountDisplay.textContent = 'Меньше минимума';
    if (efficiencyDisplay) efficiencyDisplay.textContent = '-';

    if (actionStatus) {
      actionStatus.className = 'action-status-card insufficient-minimum';
      actionStatus.querySelector('.status-icon').textContent = '⚠️';
      const minCoinsFormatted = data.minCoins.toLocaleString();
      const minUsd = (data.minCoins * 0.001).toFixed(2);
      actionStatus.querySelector('.status-text').textContent = window.appLocalization ?
        window.appLocalization.get('earnings.minimum_amount', {amount: minCoinsFormatted, usd: `$${minUsd}`}) :
        `Минимум: ${minCoinsFormatted} монет ($${minUsd})`;
    }

  } else {
    // Достаточно для вывода
    const afterFee = dollarAmount - data.networkFee;
    const efficiency = afterFee > 0 ? ((afterFee / dollarAmount) * 100) : 0;

    if (withdrawalAmountDisplay) withdrawalAmountDisplay.textContent = `$${dollarAmount.toFixed(3)}`;
    if (feeAmountDisplay) feeAmountDisplay.textContent = `$${data.networkFee}`;

    if (afterFee > 0) {
      if (finalAmountDisplay) finalAmountDisplay.textContent = `$${afterFee.toFixed(3)}`;
      if (efficiencyDisplay) efficiencyDisplay.textContent = `${efficiency.toFixed(1)}%`;

      if (actionStatus) {
        actionStatus.className = 'action-status-card available';
        actionStatus.querySelector('.status-icon').textContent = '✅';
        actionStatus.querySelector('.status-text').textContent = window.appLocalization ?
          window.appLocalization.get('earnings.ready_for_withdrawal', {amount: `$${afterFee.toFixed(3)}`}) :
          `Готово к выводу! Получите $${afterFee.toFixed(3)}`;
      }
    } else {
      if (finalAmountDisplay) finalAmountDisplay.textContent = 'Убыток';
      if (efficiencyDisplay) efficiencyDisplay.textContent = '0%';

      if (actionStatus) {
        actionStatus.className = 'action-status-card loss';
        actionStatus.querySelector('.status-icon').textContent = '💸';
        actionStatus.querySelector('.status-text').textContent = window.appLocalization ?
          window.appLocalization.get('earnings.fee_exceeds_amount') :
          'Комиссия больше суммы вывода';
      }
    }
  }
}

/**
 * Обновляет статус табов валют (доступность)
 */
function updateCurrencyTabsStatus(coinAmount) {
  const currencyTabs = document.querySelectorAll('.currency-tab');
  const userBalance = currentUserBalance || 0;

  currencyTabs.forEach(tab => {
    const currency = tab.dataset.currency;
    const data = currencyData[currency];

    if (!data) return;

    // Убираем класс недоступности
    tab.classList.remove('insufficient');

    // Проверяем условия недоступности
    if (coinAmount > userBalance || (coinAmount > 0 && coinAmount < data.minCoins)) {
      tab.classList.add('insufficient');
    }
  });
}

/**
 * Обновляет расчет для конкретной валюты
 */
function updateCurrencyCalculation(currency, coinAmount, dollarAmount) {
  const data = currencyData[currency];
  const calcElement = document.getElementById(`calc-${currency}`);
  const statusElement = document.getElementById(`status-${currency}`);
  const currencyCard = document.querySelector(`[data-currency="${currency}"]`);

  if (!calcElement || !data) return;

  const amountSpan = calcElement.querySelector('.amount');
  const percentSpan = calcElement.querySelector('.percent');

  // Сброс классов
  if (currencyCard) {
    currencyCard.classList.remove('insufficient', 'selected');
  }

  // Проверяем баланс пользователя
  const userBalance = currentUserBalance || 0;

  if (coinAmount > userBalance) {
    // Недостаточно средств на балансе
    amountSpan.textContent = 'Недостаточно средств';
    percentSpan.textContent = `Нужно: ${coinAmount - userBalance} монет`;

    if (statusElement) {
      statusElement.textContent = 'Недостаточно средств';
      statusElement.className = 'action-status insufficient-funds';
    }

    if (currencyCard) {
      currencyCard.classList.add('insufficient');
    }

  } else if (coinAmount < data.minCoins) {
    // Недостаточно для минимума
    amountSpan.textContent = `Мин: ${data.minCoins.toLocaleString()} монет`;
    percentSpan.textContent = `($${(data.minCoins * 0.001).toFixed(2)})`;

    if (statusElement) {
      statusElement.textContent = `Минимум: ${data.minCoins.toLocaleString()} монет`;
      statusElement.className = 'action-status insufficient-minimum';
    }

    if (currencyCard) {
      currencyCard.classList.add('insufficient');
    }

  } else {
    // Достаточно для вывода
    const afterFee = dollarAmount - data.networkFee;
    const efficiency = afterFee > 0 ? ((afterFee / dollarAmount) * 100) : 0;

    if (afterFee > 0) {
      amountSpan.textContent = `$${afterFee.toFixed(3)}`;
      percentSpan.textContent = `Эффективность: ${efficiency.toFixed(1)}%`;

      if (statusElement) {
        statusElement.textContent = '✅ Доступно для вывода';
        statusElement.className = 'action-status available';
      }

      if (currencyCard) {
        currencyCard.classList.remove('insufficient');
      }

    } else {
      amountSpan.textContent = 'Убыток';
      percentSpan.textContent = `Комиссия больше суммы`;

      if (statusElement) {
        statusElement.textContent = '⚠️ Комиссия больше суммы';
        statusElement.className = 'action-status loss';
      }

      if (currencyCard) {
        currencyCard.classList.add('insufficient');
      }
    }
  }
}

/**
 * Обновляет проверку баланса
 */
function updateBalanceCheck(amount) {
  const balanceCheck = document.getElementById('balance-check');
  const calcBalance = document.getElementById('calc-balance');

  // Обновляем баланс в заголовке калькулятора
  if (calcBalance) {
    calcBalance.textContent = `${currentUserBalance || 0} монет`;
  }

  if (balanceCheck) {
    const userBalance = currentUserBalance || 0;

    if (amount === 0) {
      balanceCheck.textContent = 'Введите сумму';
      balanceCheck.className = 'balance-status neutral';
    } else if (amount > userBalance) {
      balanceCheck.textContent = `Недостаточно! Доступно: ${userBalance} монет`;
      balanceCheck.className = 'balance-status insufficient';
    } else {
      balanceCheck.textContent = `Достаточно средств (${userBalance} монет)`;
      balanceCheck.className = 'balance-status sufficient';
    }
  }
}

/**
 * Выбирает валюту и обновляет форму вывода
 */
function selectCurrency(currency) {
  const currencyCards = document.querySelectorAll('.currency-card');
  const calcAmountInput = document.getElementById('calc-amount');
  const withdrawalAmountInput = document.getElementById('withdrawal-amount');
  const cryptoCurrencySelect = document.getElementById('crypto-currency');

  // Убираем выделение со всех валют
  currencyCards.forEach(card => {
    card.classList.remove('selected');
  });

  // Выделяем выбранную валюту
  const selectedCard = document.querySelector(`[data-currency="${currency}"]`);
  if (selectedCard && !selectedCard.classList.contains('insufficient')) {
    selectedCard.classList.add('selected');

    // Обновляем форму вывода
    const amount = parseInt(calcAmountInput?.value) || 0;

    if (withdrawalAmountInput) {
      withdrawalAmountInput.value = amount;
    }

    if (cryptoCurrencySelect) {
      cryptoCurrencySelect.value = currency;
    }

    // Обновляем расчет суммы к получению
    updateCryptoAmountField();

    // Обновляем placeholder адреса
    updateAddressPlaceholder();

    // Валидируем форму
    validateWithdrawalForm();

    // Показываем уведомление
    showCurrencySelectedNotification(currency);
  }
}

/**
 * Показывает уведомление о выборе валюты
 */
function showCurrencySelectedNotification(currency) {
  const data = currencyData[currency];
  if (!data) return;

  // Создаем временное уведомление
  const notification = document.createElement('div');
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--app-primary-color);
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    z-index: 1000;
    animation: slideIn 0.3s ease;
  `;
  notification.textContent = `✅ Выбрана валюта: ${data.name}`;

  document.body.appendChild(notification);

  // Удаляем через 3 секунды
  setTimeout(() => {
    notification.style.animation = 'slideOut 0.3s ease';
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 300);
  }, 3000);
}

/**
 * Загружает историю выплат
 */
function loadWithdrawalHistory() {
  const historyList = document.getElementById('withdrawal-history-list');
  if (!historyList) return;

  // Показываем загрузку
  historyList.innerHTML = `
    <div class="history-placeholder">
      <div class="placeholder-icon">⏳</div>
      <div class="placeholder-text">Загрузка истории...</div>
    </div>
  `;

  // Загружаем данные с сервера
  fetch('api/get_user_data.php', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      user_id: currentUserId,
      action: 'get_withdrawal_history'
    })
  })
  .then(response => response.json())
  .then(data => {
    if (data.success && data.withdrawals) {
      displayWithdrawalHistory(data.withdrawals);
    } else {
      showEmptyHistory();
    }
  })
  .catch(error => {
    console.error('Ошибка загрузки истории:', error);
    showHistoryError();
  });
}

/**
 * Отображает историю выплат
 */
function displayWithdrawalHistory(withdrawals) {
  const historyList = document.getElementById('withdrawal-history-list');
  if (!historyList) return;

  if (!withdrawals || withdrawals.length === 0) {
    showEmptyHistory();
    return;
  }

  historyList.innerHTML = withdrawals.map(withdrawal => {
    const date = new Date(withdrawal.created_at);
    const dateStr = date.toLocaleDateString('ru-RU');
    const timeStr = date.toLocaleTimeString('ru-RU', { hour: '2-digit', minute: '2-digit' });

    const statusText = getStatusText(withdrawal.status);
    const statusClass = getStatusClass(withdrawal.status);

    return `
      <div class="history-item">
        <div class="withdrawal-header">
          <span class="withdrawal-amount">${withdrawal.coins_amount} монет</span>
          <span class="withdrawal-status ${statusClass}">${statusText}</span>
        </div>
        <div class="withdrawal-details">
          <div class="withdrawal-currency">${withdrawal.currency.toUpperCase()}</div>
          <div class="withdrawal-date">${dateStr} ${timeStr}</div>
          ${withdrawal.wallet_address ? `<div class="withdrawal-address">${withdrawal.wallet_address}</div>` : ''}
        </div>
      </div>
    `;
  }).join('');
}

/**
 * Показывает пустую историю
 */
function showEmptyHistory() {
  const historyList = document.getElementById('withdrawal-history-list');
  if (!historyList) return;

  historyList.innerHTML = `
    <div class="history-placeholder">
      <div class="placeholder-icon">📝</div>
      <div class="placeholder-text">История выплат пуста</div>
      <div class="placeholder-hint">Ваши заявки на вывод будут отображаться здесь</div>
    </div>
  `;
}

/**
 * Показывает ошибку загрузки истории
 */
function showHistoryError() {
  const historyList = document.getElementById('withdrawal-history-list');
  if (!historyList) return;

  historyList.innerHTML = `
    <div class="history-placeholder">
      <div class="placeholder-icon">❌</div>
      <div class="placeholder-text">Ошибка загрузки</div>
      <div class="placeholder-hint">Попробуйте обновить историю</div>
    </div>
  `;
}

/**
 * Обновляет историю выплат
 */
function refreshWithdrawalHistory() {
  const refreshButton = document.getElementById('refresh-history');
  if (refreshButton) {
    refreshButton.classList.add('loading');
    refreshButton.disabled = true;
  }

  loadWithdrawalHistory();

  // Убираем состояние загрузки через 2 секунды
  setTimeout(() => {
    if (refreshButton) {
      refreshButton.classList.remove('loading');
      refreshButton.disabled = false;
    }
  }, 2000);
}

/**
 * Получает текст статуса на русском
 */
function getStatusText(status) {
  const statusMap = {
    'pending': 'Ожидание',
    'processing': 'Обработка',
    'completed': 'Завершено',
    'failed': 'Ошибка',
    'cancelled': 'Отменено'
  };
  return statusMap[status] || status;
}

/**
 * Получает CSS класс для статуса
 */
function getStatusClass(status) {
  return status; // pending, processing, completed, failed, cancelled
}

console.log("main.js загружен.");
