<?php
/**
 * Реальный тест с выплатой на кошелек пользователя
 */

require_once 'api/config.php';
require_once 'api/NOWPaymentsAPI.php';

echo "💰 РЕАЛЬНЫЙ ТЕСТ С ВЫПЛАТОЙ НА ВАШ КОШЕЛЕК\n";
echo "==========================================\n\n";

$api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);

// Ваш реальный TRON кошелек
$userWallet = 'TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK';

echo "🎯 ПАРАМЕТРЫ ТЕСТА:\n";
echo "===================\n";
echo "💳 Ваш кошелек: {$userWallet}\n";
echo "🏦 Тип: TRON (TRX/USDT TRC20)\n";
echo "💵 Сумма: Минимальная тестовая\n\n";

echo "💰 1. ПРОВЕРКА БАЛАНСА\n";
echo "======================\n";

$balance = $api->getAccountBalance();
if ($balance) {
    echo "✅ Текущий баланс:\n";
    $totalUSD = 0;
    
    foreach ($balance as $currency => $data) {
        $amount = is_array($data) ? ($data['amount'] ?? 0) : $data;
        if ($amount > 0) {
            echo "   💰 {$currency}: {$amount}\n";
            
            try {
                $estimate = $api->getEstimateAmount($amount, $currency, 'usd');
                if (isset($estimate['estimated_amount'])) {
                    $usdValue = $estimate['estimated_amount'];
                    $totalUSD += $usdValue;
                    echo "      ≈ {$usdValue} USD\n";
                }
            } catch (Exception $e) {
                echo "      (оценка недоступна)\n";
            }
        }
    }
    
    echo "\n💵 Общая стоимость: ~{$totalUSD} USD\n";
    
    if ($totalUSD < 0.1) {
        echo "⚠️  Баланс очень мал для тестирования\n";
    }
} else {
    echo "❌ Не удалось получить баланс\n";
    exit;
}

echo "\n🎯 2. ТЕСТ 1: ПРЯМАЯ ВЫПЛАТА USDT TRC20\n";
echo "=======================================\n";

$testAmount = '0.01'; // $0.01 USDT
echo "📝 Попытка прямой выплаты:\n";
echo "   Сумма: {$testAmount} USDT TRC20\n";
echo "   Адрес: {$userWallet}\n";
echo "   Ожидание: Успех (если есть USDT TRC20 баланс)\n\n";

try {
    $directResult = $api->createSinglePayout($userWallet, 'usdttrc20', $testAmount);
    
    if ($directResult && !isset($directResult['error'])) {
        echo "🎉 ПРЯМАЯ ВЫПЛАТА СОЗДАНА!\n";
        echo "   ID: " . ($directResult['id'] ?? 'N/A') . "\n";
        
        if (isset($directResult['withdrawals'][0])) {
            $w = $directResult['withdrawals'][0];
            echo "   Статус: " . ($w['status'] ?? 'N/A') . "\n";
            echo "   Адрес: " . ($w['address'] ?? 'N/A') . "\n";
            echo "   Валюта: " . ($w['currency'] ?? 'N/A') . "\n";
            echo "   Сумма: " . ($w['amount'] ?? 'N/A') . "\n";
            
            if ($w['address'] === $userWallet) {
                echo "   ✅ ОТЛИЧНО: Выплата на ваш кошелек!\n";
            }
        }
        
        echo "\n🎉 ТЕСТ ЗАВЕРШЕН УСПЕШНО - ПРЯМАЯ ВЫПЛАТА РАБОТАЕТ!\n";
        exit; // Прямая выплата удалась, дальше тестировать не нужно
        
    } else {
        echo "⚠️  Прямая выплата не удалась:\n";
        echo "   Ошибка: " . ($directResult['message'] ?? 'Unknown') . "\n";
        echo "   Код: " . ($directResult['code'] ?? 'Unknown') . "\n";
        echo "   Переходим к автоконвертации...\n";
    }
    
} catch (Exception $e) {
    echo "💥 Ошибка прямой выплаты: " . $e->getMessage() . "\n";
    echo "   Переходим к автоконвертации...\n";
}

echo "\n🔄 3. ТЕСТ 2: АВТОКОНВЕРТАЦИЯ НА ВАШ КОШЕЛЕК\n";
echo "============================================\n";

echo "📝 Параметры автоконвертации:\n";
echo "   Запрос: {$testAmount} USDT TRC20\n";
echo "   Ваш адрес: {$userWallet} (TRON)\n";
echo "   Доступно: BTC в балансе\n";
echo "   Ожидание: Автоконвертация в валюту, совместимую с TRON\n\n";

try {
    $autoResult = $api->createPayoutWithAutoConversion($userWallet, 'usdttrc20', $testAmount);
    
    if ($autoResult && !isset($autoResult['error'])) {
        echo "🎉 АВТОКОНВЕРТАЦИЯ УСПЕШНА!\n";
        echo "   ID выплаты: " . ($autoResult['id'] ?? 'N/A') . "\n";
        
        // Детали автоконвертации
        if (isset($autoResult['auto_conversion'])) {
            $conv = $autoResult['auto_conversion'];
            echo "\n🔄 Детали конвертации:\n";
            echo "   Запрошено: {$conv['original_request']['amount']} {$conv['original_request']['currency']}\n";
            echo "   Выплачено: {$conv['actual_payout']['amount']} {$conv['actual_payout']['currency']}\n";
            echo "   Адрес: {$conv['actual_payout']['address']}\n";
            
            // КРИТИЧЕСКАЯ ПРОВЕРКА
            if ($conv['actual_payout']['address'] === $userWallet) {
                echo "   ✅ ОТЛИЧНО: Деньги идут на ваш кошелек!\n";
            } else {
                echo "   ❌ ОШИБКА: Адрес заменен на {$conv['actual_payout']['address']}\n";
                echo "   💸 ДЕНЬГИ НЕ ДОЙДУТ ДО ВАС!\n";
            }
            
            // Проверка валюты
            $payoutCurrency = $conv['actual_payout']['currency'];
            if ($payoutCurrency === 'usdttrc20' || $payoutCurrency === 'trx') {
                echo "   ✅ ПРАВИЛЬНО: Валюта {$payoutCurrency} совместима с TRON\n";
            } else {
                echo "   ⚠️  ВНИМАНИЕ: Валюта {$payoutCurrency} может быть несовместима с TRON\n";
            }
        }
        
        // Детали выплаты
        if (isset($autoResult['withdrawals'][0])) {
            $w = $autoResult['withdrawals'][0];
            echo "\n📋 Детали выплаты:\n";
            echo "   ID: " . ($w['id'] ?? 'N/A') . "\n";
            echo "   Статус: " . ($w['status'] ?? 'N/A') . "\n";
            echo "   Валюта: " . ($w['currency'] ?? 'N/A') . "\n";
            echo "   Сумма: " . ($w['amount'] ?? 'N/A') . "\n";
            echo "   Адрес: " . ($w['address'] ?? 'N/A') . "\n";
            
            // Финальная проверка адреса
            if (isset($w['address']) && $w['address'] === $userWallet) {
                echo "   ✅ ПОДТВЕРЖДЕНО: Выплата на ваш кошелек!\n";
                
                // Проверяем статус
                if (isset($w['status'])) {
                    switch ($w['status']) {
                        case 'CREATING':
                            echo "   🔄 Статус: Создается - это нормально\n";
                            break;
                        case 'REJECTED':
                            echo "   ❌ Статус: Отклонена - возможны проблемы\n";
                            break;
                        case 'COMPLETED':
                            echo "   ✅ Статус: Завершена - деньги отправлены!\n";
                            break;
                        default:
                            echo "   ℹ️  Статус: {$w['status']}\n";
                    }
                }
            } else {
                echo "   ❌ КРИТИЧЕСКАЯ ОШИБКА: Неправильный адрес выплаты!\n";
            }
        }
        
        echo "\n🎉 РЕАЛЬНАЯ ВЫПЛАТА НА ВАШ КОШЕЛЕК СОЗДАНА!\n";
        echo "===========================================\n";
        echo "💰 Проверьте ваш кошелек через несколько минут\n";
        echo "🔍 Адрес для проверки: {$userWallet}\n";
        echo "📱 Используйте TRON explorer для отслеживания\n";
        
    } else {
        echo "❌ АВТОКОНВЕРТАЦИЯ НЕ УДАЛАСЬ:\n";
        echo "   Код: " . ($autoResult['code'] ?? 'UNKNOWN') . "\n";
        echo "   Сообщение: " . ($autoResult['message'] ?? 'Unknown error') . "\n";
        
        if (isset($autoResult['details'])) {
            echo "\n📋 Детали ошибки:\n";
            foreach ($autoResult['details'] as $key => $value) {
                echo "   {$key}: {$value}\n";
            }
        }
        
        if ($autoResult['code'] === 'INCOMPATIBLE_ADDRESS') {
            echo "\n💡 ОБЪЯСНЕНИЕ:\n";
            echo "   Ваш TRON адрес несовместим с доступными валютами\n";
            echo "   Система правильно защищает ваши средства\n";
        }
    }
    
} catch (Exception $e) {
    echo "💥 Исключение: " . $e->getMessage() . "\n";
}

echo "\n🏁 ИТОГИ РЕАЛЬНОГО ТЕСТА:\n";
echo "==========================\n";
echo "🎯 Тестировали выплату на ваш реальный кошелек\n";
echo "🔒 Система защищает адреса пользователей\n";
echo "💰 Выплаты идут на правильные адреса\n";
echo "📱 Вы получите уведомление при поступлении средств\n";

echo "\n🚀 Реальный тест завершен!\n";
?>
