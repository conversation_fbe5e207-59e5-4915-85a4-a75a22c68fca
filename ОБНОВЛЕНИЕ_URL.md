# 🔄 Обновление URL для app.uniqpaid.com/test2/

## ✅ ВСЕ URL ОБНОВЛЕНЫ!

### 📁 Структура на сервере:
```
app.uniqpaid.com/test2/
├── index.html
├── main.js
├── styles.css
├── api/
├── bot/
├── images/
└── ...
```

### 🔧 Обновленные файлы:

#### 1. `bot/config.php`:
```php
define('WEBHOOK_URL', 'https://app.uniqpaid.com/test2/bot/webhook.php');
define('WEBAPP_URL', 'https://app.uniqpaid.com/test2/');
define('BOT_USERNAME', 'uniqpaid_bot');
```

#### 2. `bot/webhook.php`:
```php
$photoUrl = 'https://app.uniqpaid.com/test2/images/welcome.svg';
```

#### 3. `api/NOWPaymentsAPI.php`:
```php
'ipn_callback_url' => 'https://app.uniqpaid.com/test2/api/withdrawal_callback.php'
```

#### 4. `main.js`:
```javascript
const BOT_USERNAME = "uniqpaid_bot";
```

### 🚀 Команды для установки после загрузки:

#### 1. Установка webhook:
```bash
curl -X POST "https://api.telegram.org/bot8105471536:AAH5hl2iouOCmnm0yj5MteqnGpziCvChcbc/setWebhook" \
     -d "url=https://app.uniqpaid.com/test2/bot/webhook.php"
```

#### 2. Или через PHP:
```bash
php bot/setup.php
```

### 🧪 Проверка после загрузки:

#### 1. Проверьте доступность:
- ✅ https://app.uniqpaid.com/test2/ - главная страница
- ✅ https://app.uniqpaid.com/test2/bot/webhook.php - webhook (пустая страница)
- ✅ https://app.uniqpaid.com/test2/images/welcome.svg - изображение

#### 2. Настройте бота в @BotFather:
- Имя: `UniQPaid Bot`
- Username: `uniqpaid_bot`
- WebApp URL: `https://app.uniqpaid.com/test2/`

#### 3. Добавьте IP сервера в NOWPayments whitelist

### 📋 Итоговые URL:

| Компонент | URL |
|-----------|-----|
| **Мини-приложение** | https://app.uniqpaid.com/test2/ |
| **Webhook** | https://app.uniqpaid.com/test2/bot/webhook.php |
| **Callback** | https://app.uniqpaid.com/test2/api/withdrawal_callback.php |
| **Приветствие** | https://app.uniqpaid.com/test2/images/welcome.svg |
| **Админ панель** | https://app.uniqpaid.com/test2/api/admin/login.php |

### 🎉 ГОТОВО!

Все файлы обновлены для работы с URL структурой:
`https://app.uniqpaid.com/test2/`

Загружайте файлы на сервер и тестируйте! 🚀
