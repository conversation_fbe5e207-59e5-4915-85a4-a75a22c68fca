<?php
/**
 * api/admin/stats.php
 * Страница статистики
 */

// Включаем логирование
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../error.log');
error_reporting(E_ALL);

// Подключаем файл с функциями аутентификации и авторизации
require_once __DIR__ . '/auth.php';

// Проверяем аутентификацию
session_start();
if (!isAuthenticated()) {
    // Если пользователь не аутентифицирован, перенаправляем на страницу входа
    header('Location: login.php');
    exit;
}

// --- Подключение зависимостей с проверкой ---
if (!(@require_once __DIR__ . '/../config.php')) { 
    http_response_code(500); 
    error_log('FATAL: config.php not found in admin/stats.php'); 
    die('Ошибка: Не удалось загрузить config.php'); 
}
if (!(@require_once __DIR__ . '/../db_mock.php')) { 
    http_response_code(500); 
    error_log('FATAL: db_mock.php not found in admin/stats.php'); 
    die('Ошибка: Не удалось загрузить db_mock.php'); 
}
// --- Конец проверки зависимостей ---

// Загрузка данных пользователей
$userData = loadUserData();
if (!is_array($userData)) {
    die('Ошибка: Не удалось загрузить данные пользователей');
}

// Получение статистики
$totalUsers = count($userData);
$totalBalance = 0;
$totalReferrals = 0;
$totalWithdrawals = 0;
$totalWithdrawalAmount = 0;
$totalAdViews = 0;
$totalEarned = 0;
$activeUsers = 0;
$blockedUsers = 0;

// Статистика по дням (последние 30 дней)
$days = 30;
$dailyStats = [];
for ($i = 0; $i < $days; $i++) {
    $date = date('Y-m-d', strtotime("-$i days"));
    $dailyStats[$date] = [
        'new_users' => 0,
        'ad_views' => 0,
        'withdrawals' => 0
    ];
}

// Статистика по рефералам
$referralStats = [
    'users_with_referrals' => 0,
    'max_referrals' => 0,
    'user_with_max_referrals' => 0,
    'total_referral_earnings' => 0
];

// Статистика по выводам
$withdrawalStats = [
    'users_with_withdrawals' => 0,
    'max_withdrawals' => 0,
    'user_with_max_withdrawals' => 0,
    'average_withdrawal' => 0
];

// Статистика по криптовалютам
$cryptoStats = [];

foreach ($userData as $userId => $user) {
    // Общая статистика
    $totalBalance += $user['balance'] ?? 0;
    $totalReferrals += $user['referrals_count'] ?? 0;
    $totalEarned += $user['total_earned'] ?? 0;
    
    // Статистика по пользователям
    if (isset($user['blocked']) && $user['blocked']) {
        $blockedUsers++;
    } else {
        $activeUsers++;
    }
    
    // Статистика по дням
    if (isset($user['joined'])) {
        $joinDate = date('Y-m-d', $user['joined']);
        if (isset($dailyStats[$joinDate])) {
            $dailyStats[$joinDate]['new_users']++;
        }
    }
    
    // Статистика по просмотрам рекламы
    if (isset($user['ad_views_log']) && is_array($user['ad_views_log'])) {
        $totalAdViews += count($user['ad_views_log']);
        
        foreach ($user['ad_views_log'] as $timestamp) {
            $viewDate = date('Y-m-d', $timestamp);
            if (isset($dailyStats[$viewDate])) {
                $dailyStats[$viewDate]['ad_views']++;
            }
        }
    }
    
    // Статистика по рефералам
    if (isset($user['referrals_count']) && $user['referrals_count'] > 0) {
        $referralStats['users_with_referrals']++;
        
        if ($user['referrals_count'] > $referralStats['max_referrals']) {
            $referralStats['max_referrals'] = $user['referrals_count'];
            $referralStats['user_with_max_referrals'] = $userId;
        }
        
        $referralStats['total_referral_earnings'] += $user['referral_earnings'] ?? 0;
    }
    
    // Статистика по выводам
    if (isset($user['withdrawals']) && is_array($user['withdrawals'])) {
        $withdrawalsCount = count($user['withdrawals']);
        $totalWithdrawals += $withdrawalsCount;
        
        if ($withdrawalsCount > 0) {
            $withdrawalStats['users_with_withdrawals']++;
            
            if ($withdrawalsCount > $withdrawalStats['max_withdrawals']) {
                $withdrawalStats['max_withdrawals'] = $withdrawalsCount;
                $withdrawalStats['user_with_max_withdrawals'] = $userId;
            }
            
            foreach ($user['withdrawals'] as $withdrawal) {
                $totalWithdrawalAmount += $withdrawal['coins_amount'] ?? 0;
                
                // Статистика по дням
                if (isset($withdrawal['timestamp'])) {
                    $withdrawalDate = date('Y-m-d', $withdrawal['timestamp']);
                    if (isset($dailyStats[$withdrawalDate])) {
                        $dailyStats[$withdrawalDate]['withdrawals']++;
                    }
                }
                
                // Статистика по криптовалютам
                if (isset($withdrawal['currency'])) {
                    $currency = $withdrawal['currency'];
                    if (!isset($cryptoStats[$currency])) {
                        $cryptoStats[$currency] = [
                            'count' => 0,
                            'amount' => 0
                        ];
                    }
                    $cryptoStats[$currency]['count']++;
                    $cryptoStats[$currency]['amount'] += $withdrawal['coins_amount'] ?? 0;
                }
            }
        }
    }
}

// Вычисляем средний размер вывода
$withdrawalStats['average_withdrawal'] = $totalWithdrawals > 0 ? $totalWithdrawalAmount / $totalWithdrawals : 0;

// Сортируем статистику по дням
ksort($dailyStats);

// Сортируем статистику по криптовалютам
arsort($cryptoStats);

// Подключаем шаблон заголовка
include 'templates/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Боковое меню -->
        <nav id="sidebar" class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
            <div class="position-sticky pt-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="bi bi-speedometer2 me-2"></i>
                            Панель управления
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="users.php">
                            <i class="bi bi-people me-2"></i>
                            Пользователи
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="stats.php">
                            <i class="bi bi-bar-chart me-2"></i>
                            Статистика
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="security.php">
                            <i class="bi bi-shield-lock me-2"></i>
                            Безопасность
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="settings.php">
                            <i class="bi bi-gear me-2"></i>
                            Настройки
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="logout.php">
                            <i class="bi bi-box-arrow-right me-2"></i>
                            Выход
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- Основное содержимое -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Статистика</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="stats.php" class="btn btn-sm btn-outline-secondary">Обновить</a>
                    </div>
                </div>
            </div>

            <!-- Общая статистика -->
            <div class="row">
                <div class="col-md-3 mb-4">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        Пользователей</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $totalUsers; ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-people fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-3 mb-4">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        Общий баланс</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $totalBalance; ?> монет</div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-currency-exchange fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-3 mb-4">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        Просмотров рекламы</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $totalAdViews; ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-eye fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-3 mb-4">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        Выводов средств</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $totalWithdrawals; ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-cash-stack fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Графики -->
            <div class="row">
                <div class="col-md-12 mb-4">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Статистика за последние 30 дней</h6>
                        </div>
                        <div class="card-body">
                            <canvas id="dailyStatsChart" width="100%" height="30"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Статистика по пользователям -->
                <div class="col-md-6 mb-4">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Статистика по пользователям</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <tbody>
                                        <tr>
                                            <td>Всего пользователей</td>
                                            <td><?php echo $totalUsers; ?></td>
                                        </tr>
                                        <tr>
                                            <td>Активных пользователей</td>
                                            <td><?php echo $activeUsers; ?></td>
                                        </tr>
                                        <tr>
                                            <td>Заблокированных пользователей</td>
                                            <td><?php echo $blockedUsers; ?></td>
                                        </tr>
                                        <tr>
                                            <td>Общий баланс</td>
                                            <td><?php echo $totalBalance; ?> монет</td>
                                        </tr>
                                        <tr>
                                            <td>Всего заработано</td>
                                            <td><?php echo $totalEarned; ?> монет</td>
                                        </tr>
                                        <tr>
                                            <td>Всего выведено</td>
                                            <td><?php echo $totalWithdrawalAmount; ?> монет</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Статистика по рефералам -->
                <div class="col-md-6 mb-4">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Статистика по рефералам</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <tbody>
                                        <tr>
                                            <td>Всего рефералов</td>
                                            <td><?php echo $totalReferrals; ?></td>
                                        </tr>
                                        <tr>
                                            <td>Пользователей с рефералами</td>
                                            <td><?php echo $referralStats['users_with_referrals']; ?></td>
                                        </tr>
                                        <tr>
                                            <td>Максимальное количество рефералов у пользователя</td>
                                            <td><?php echo $referralStats['max_referrals']; ?> (ID: <?php echo $referralStats['user_with_max_referrals']; ?>)</td>
                                        </tr>
                                        <tr>
                                            <td>Всего заработано на рефералах</td>
                                            <td><?php echo $referralStats['total_referral_earnings']; ?> монет</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Статистика по выводам -->
                <div class="col-md-6 mb-4">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Статистика по выводам</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <tbody>
                                        <tr>
                                            <td>Всего выводов</td>
                                            <td><?php echo $totalWithdrawals; ?></td>
                                        </tr>
                                        <tr>
                                            <td>Пользователей с выводами</td>
                                            <td><?php echo $withdrawalStats['users_with_withdrawals']; ?></td>
                                        </tr>
                                        <tr>
                                            <td>Максимальное количество выводов у пользователя</td>
                                            <td><?php echo $withdrawalStats['max_withdrawals']; ?> (ID: <?php echo $withdrawalStats['user_with_max_withdrawals']; ?>)</td>
                                        </tr>
                                        <tr>
                                            <td>Средний размер вывода</td>
                                            <td><?php echo round($withdrawalStats['average_withdrawal'], 2); ?> монет</td>
                                        </tr>
                                        <tr>
                                            <td>Всего выведено</td>
                                            <td><?php echo $totalWithdrawalAmount; ?> монет</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Статистика по криптовалютам -->
                <div class="col-md-6 mb-4">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Статистика по криптовалютам</h6>
                        </div>
                        <div class="card-body">
                            <?php if (empty($cryptoStats)): ?>
                                <p class="text-center">Нет данных о выводах в криптовалюте</p>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>Криптовалюта</th>
                                                <th>Количество выводов</th>
                                                <th>Всего выведено (монет)</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($cryptoStats as $currency => $stats): ?>
                                                <tr>
                                                    <td><?php echo $currency; ?></td>
                                                    <td><?php echo $stats['count']; ?></td>
                                                    <td><?php echo $stats['amount']; ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
    // Данные для графика
    const dates = <?php echo json_encode(array_keys($dailyStats)); ?>;
    const newUsers = <?php echo json_encode(array_column($dailyStats, 'new_users')); ?>;
    const adViews = <?php echo json_encode(array_column($dailyStats, 'ad_views')); ?>;
    const withdrawals = <?php echo json_encode(array_column($dailyStats, 'withdrawals')); ?>;
    
    // Создаем график
    const ctx = document.getElementById('dailyStatsChart').getContext('2d');
    const dailyStatsChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: dates,
            datasets: [
                {
                    label: 'Новые пользователи',
                    data: newUsers,
                    borderColor: 'rgba(78, 115, 223, 1)',
                    backgroundColor: 'rgba(78, 115, 223, 0.1)',
                    borderWidth: 2,
                    pointRadius: 3,
                    pointBackgroundColor: 'rgba(78, 115, 223, 1)',
                    pointBorderColor: 'rgba(78, 115, 223, 1)',
                    pointHoverRadius: 5,
                    pointHoverBackgroundColor: 'rgba(78, 115, 223, 1)',
                    pointHoverBorderColor: 'rgba(78, 115, 223, 1)',
                    pointHitRadius: 10,
                    pointBorderWidth: 2,
                    tension: 0.3
                },
                {
                    label: 'Просмотры рекламы',
                    data: adViews,
                    borderColor: 'rgba(28, 200, 138, 1)',
                    backgroundColor: 'rgba(28, 200, 138, 0.1)',
                    borderWidth: 2,
                    pointRadius: 3,
                    pointBackgroundColor: 'rgba(28, 200, 138, 1)',
                    pointBorderColor: 'rgba(28, 200, 138, 1)',
                    pointHoverRadius: 5,
                    pointHoverBackgroundColor: 'rgba(28, 200, 138, 1)',
                    pointHoverBorderColor: 'rgba(28, 200, 138, 1)',
                    pointHitRadius: 10,
                    pointBorderWidth: 2,
                    tension: 0.3
                },
                {
                    label: 'Выводы средств',
                    data: withdrawals,
                    borderColor: 'rgba(246, 194, 62, 1)',
                    backgroundColor: 'rgba(246, 194, 62, 0.1)',
                    borderWidth: 2,
                    pointRadius: 3,
                    pointBackgroundColor: 'rgba(246, 194, 62, 1)',
                    pointBorderColor: 'rgba(246, 194, 62, 1)',
                    pointHoverRadius: 5,
                    pointHoverBackgroundColor: 'rgba(246, 194, 62, 1)',
                    pointHoverBorderColor: 'rgba(246, 194, 62, 1)',
                    pointHitRadius: 10,
                    pointBorderWidth: 2,
                    tension: 0.3
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: {
                    grid: {
                        display: false
                    }
                },
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)'
                    }
                }
            },
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                }
            }
        }
    });
</script>

<?php
// Подключаем шаблон подвала
include 'templates/footer.php';
?>
