<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Управление балансом NOWPayments</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .balance-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .balance-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        .balance-item:last-child {
            border-bottom: none;
        }
        .currency {
            font-weight: bold;
            color: #2c3e50;
        }
        .amount {
            color: #27ae60;
            font-size: 1.1em;
        }
        .low-balance {
            color: #e74c3c;
        }
        .action-section {
            margin-top: 30px;
            padding: 20px;
            border: 2px solid #3498db;
            border-radius: 8px;
            background: #ecf0f1;
        }
        .btn {
            background: #3498db;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background: #2980b9;
        }
        .btn-success {
            background: #27ae60;
        }
        .btn-success:hover {
            background: #229954;
        }
        .btn-warning {
            background: #f39c12;
        }
        .btn-warning:hover {
            background: #e67e22;
        }
        .alert {
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
        }
        .alert-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .alert-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .instructions {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏦 Управление балансом NOWPayments</h1>
        
        <?php
        require_once 'api/config.php';
        require_once 'api/NOWPaymentsAPI.php';
        
        $api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);
        
        // Получаем текущий баланс
        $balance = $api->getAccountBalance();
        ?>
        
        <div class="balance-section">
            <h2>💰 Текущий баланс</h2>
            
            <?php if ($balance): ?>
                <?php 
                $totalUSD = 0;
                $lowBalanceCurrencies = [];
                ?>
                
                <?php foreach ($balance as $currency => $data): ?>
                    <?php 
                    $amount = is_array($data) ? ($data['amount'] ?? 0) : $data;
                    $isLow = false;
                    
                    // Определяем критически низкий баланс
                    if (($currency === 'usdttrc20' || $currency === 'trx') && $amount < 1) {
                        $isLow = true;
                        $lowBalanceCurrencies[] = strtoupper($currency);
                    }
                    
                    if ($amount > 0):
                    ?>
                        <div class="balance-item">
                            <span class="currency"><?= strtoupper($currency) ?></span>
                            <span class="amount <?= $isLow ? 'low-balance' : '' ?>">
                                <?= $amount ?>
                                <?php if ($isLow): ?>
                                    ⚠️ НИЗКИЙ
                                <?php endif; ?>
                            </span>
                        </div>
                        
                        <?php
                        // Конвертируем в USD для общей суммы
                        try {
                            $estimate = $api->getEstimateAmount($amount, $currency, 'usd');
                            if (isset($estimate['estimated_amount'])) {
                                $totalUSD += $estimate['estimated_amount'];
                            }
                        } catch (Exception $e) {
                            // Игнорируем ошибки конвертации
                        }
                        ?>
                    <?php endif; ?>
                <?php endforeach; ?>
                
                <div class="balance-item" style="border-top: 2px solid #3498db; margin-top: 15px; padding-top: 15px;">
                    <span class="currency">ОБЩАЯ СТОИМОСТЬ</span>
                    <span class="amount">≈ <?= round($totalUSD, 2) ?> USD</span>
                </div>
                
            <?php else: ?>
                <div class="alert alert-warning">
                    ❌ Не удалось получить баланс. Проверьте API ключи.
                </div>
            <?php endif; ?>
        </div>
        
        <?php if (!empty($lowBalanceCurrencies)): ?>
            <div class="alert alert-warning">
                ⚠️ <strong>Внимание!</strong> Низкий баланс для TRON выплат: <?= implode(', ', $lowBalanceCurrencies) ?>
                <br>Пополните баланс для обработки выплат на TRON адреса.
            </div>
        <?php endif; ?>
        
        <div class="action-section">
            <h2>🔧 Действия</h2>
            
            <div style="margin-bottom: 20px;">
                <a href="https://account.nowpayments.io/dashboard" target="_blank" class="btn btn-success">
                    🏦 Открыть панель NOWPayments
                </a>
                
                <a href="https://account.nowpayments.io/settings/api-keys" target="_blank" class="btn">
                    🔑 Управление API ключами
                </a>
                
                <button onclick="location.reload()" class="btn btn-warning">
                    🔄 Обновить баланс
                </button>
            </div>
            
            <div class="instructions">
                <h3>📋 Инструкции по пополнению баланса</h3>
                
                <h4>🎯 Для TRON выплат (приоритет):</h4>
                <ol>
                    <li><strong>USDT TRC20</strong> - самый популярный (приоритет 1)</li>
                    <li><strong>TRX</strong> - основная валюта TRON (приоритет 5)</li>
                </ol>
                
                <h4>🔄 Как пополнить:</h4>
                <ol>
                    <li>Откройте <a href="https://account.nowpayments.io/dashboard" target="_blank">панель NOWPayments</a></li>
                    <li>Перейдите в раздел "Wallet" или "Balance"</li>
                    <li>Найдите USDT TRC20 или TRX</li>
                    <li>Нажмите "Deposit" или "Пополнить"</li>
                    <li>Переведите средства на указанный адрес</li>
                </ol>
                
                <h4>⚡ Автоконвертация:</h4>
                <ol>
                    <li>Перейдите в "Settings" → "Auto-conversion"</li>
                    <li>Включите автоконвертацию BTC → USDT TRC20</li>
                    <li>Установите лимиты и правила</li>
                </ol>
            </div>
        </div>
        
        <div class="alert alert-info">
            <h4>💡 Информация о системе выплат</h4>
            <ul>
                <li>✅ <strong>Защита адресов:</strong> Система НЕ заменяет адреса пользователей</li>
                <li>✅ <strong>Автоконвертация:</strong> Автоматически конвертирует из доступного баланса</li>
                <li>✅ <strong>Умная обработка:</strong> Находит оптимальные пути конвертации</li>
                <li>✅ <strong>Безопасность:</strong> Деньги идут только на правильные адреса</li>
            </ul>
        </div>
        
        <div style="text-align: center; margin-top: 30px; color: #7f8c8d;">
            <p>🚀 Система готова к продакшену!</p>
            <p>Как только будет баланс - все выплаты будут работать автоматически.</p>
        </div>
    </div>
</body>
</html>
