<?php
/**
 * getUserLanguage.php
 * API для получения языка пользователя
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once __DIR__ . '/db_mock.php';
require_once __DIR__ . '/../includes/Localization.php';

// Проверяем метод запроса
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// Получаем данные запроса
$input = file_get_contents('php://input');
$data = json_decode($input, true);

if (!$data || !isset($data['initData'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Missing initData']);
    exit;
}

try {
    // Парсим initData для получения информации о пользователе
    $initData = $data['initData'];

    // Простой парсинг initData
    parse_str($initData, $parsedData);

    if (!isset($parsedData['user'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid initData']);
        exit;
    }

    $user = json_decode($parsedData['user'], true);
    $userId = $user['id'];

    // Загружаем данные пользователя
    $userData = loadUserData();

    // Создаем экземпляр локализации
    $localization = Localization::getInstance();

    // Определяем язык пользователя
    $userLanguage = 'en'; // По умолчанию английский

    error_log("getUserLanguage INFO: Определение языка для пользователя {$userId}");
    error_log("getUserLanguage INFO: Данные пользователя из Telegram: " . json_encode($user));

    // Получаем IP пользователя для логирования
    $userIP = $_SERVER['HTTP_CF_CONNECTING_IP'] ?? $_SERVER['HTTP_X_FORWARDED_FOR'] ?? $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    error_log("getUserLanguage INFO: IP пользователя: {$userIP}");

    // ВСЕГДА определяем язык заново по IP (приоритет IP определению)
    $userLanguage = $localization->detectLanguage($user);
    error_log("getUserLanguage INFO: Автоматически определен язык для пользователя {$userId}: {$userLanguage}");

    // Сохраняем определенный язык в данные пользователя
    if (!isset($userData[$userId])) {
        $userData[$userId] = [];
    }
    $userData[$userId]['language'] = $userLanguage;
    saveUserData($userData);
    error_log("getUserLanguage INFO: Язык {$userLanguage} сохранен для пользователя {$userId}");

    // Возвращаем язык пользователя
    echo json_encode([
        'success' => true,
        'language' => $userLanguage,
        'user_id' => $userId
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Server error',
        'message' => $e->getMessage()
    ]);
}
?>
