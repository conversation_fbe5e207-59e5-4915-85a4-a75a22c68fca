/* 🔥 CYBERPUNK OVERLAY STYLES 🔥 */
/* Дополнительные киберпанк стили БЕЗ конфликтов с основными */

/* === CYBERPUNK COLOR PALETTE === */
:root {
  /* Neon Colors */
  --cyber-neon-blue: #00ffff;
  --cyber-neon-pink: #ff0080;
  --cyber-neon-green: #00ff41;
  --cyber-neon-purple: #8a2be2;
  --cyber-neon-orange: #ff6600;
  --cyber-neon-yellow: #ffff00;
  
  /* Cyberpunk Gradients */
  --cyber-gradient-main: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
  --cyber-gradient-neon: linear-gradient(45deg, #00ffff, #ff0080, #00ff41);
  --cyber-gradient-card: linear-gradient(145deg, #0f0f23 0%, #1a1a2e 100%);
  
  /* Cyberpunk Glows */
  --cyber-glow-blue: 0 0 20px rgba(0, 255, 255, 0.5);
  --cyber-glow-pink: 0 0 20px rgba(255, 0, 128, 0.5);
  --cyber-glow-green: 0 0 20px rgba(0, 255, 65, 0.5);
  --cyber-glow-purple: 0 0 20px rgba(138, 43, 226, 0.5);
  
  /* Cyberpunk Borders */
  --cyber-border-neon: 2px solid var(--cyber-neon-blue);
  --cyber-border-glow: 1px solid rgba(0, 255, 255, 0.3);
}

/* === CYBERPUNK FONTS === */
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap');

/* === CYBERPUNK BODY OVERLAY === */
body.cyberpunk-mode {
  font-family: 'Rajdhani', sans-serif;
  background: var(--cyber-gradient-main);
  background-image: 
    radial-gradient(circle at 20% 80%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 0, 128, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(0, 255, 65, 0.05) 0%, transparent 50%);
}

body.cyberpunk-mode::before {
  background: 
    linear-gradient(90deg, transparent 0%, rgba(0, 255, 255, 0.03) 50%, transparent 100%),
    linear-gradient(0deg, transparent 0%, rgba(255, 0, 128, 0.03) 50%, transparent 100%);
}

/* === CYBERPUNK SCAN LINE EFFECT === */
.cyber-scan-line {
  position: fixed;
  top: 0;
  left: -2px;
  width: 2px;
  height: 100vh;
  background: linear-gradient(to bottom, 
    transparent 0%, 
    var(--cyber-neon-blue) 50%, 
    transparent 100%);
  animation: cyber-scan 8s linear infinite;
  z-index: 1000;
  pointer-events: none;
}

/* === CYBERPUNK HEADER OVERLAY === */
.cyberpunk-mode .app-header {
  background: var(--cyber-gradient-card);
  border-bottom: var(--cyber-border-glow);
  backdrop-filter: blur(15px);
  box-shadow: 
    0 2px 20px rgba(0, 0, 0, 0.5),
    var(--cyber-glow-blue);
}

.cyberpunk-mode .app-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--cyber-gradient-neon);
  opacity: 0.8;
}

/* === CYBERPUNK USER AVATAR === */
.cyberpunk-mode .user-avatar {
  background: var(--cyber-gradient-neon);
  border: 2px solid var(--cyber-neon-blue);
  box-shadow: var(--cyber-glow-blue);
  position: relative;
  overflow: hidden;
}

.cyberpunk-mode .user-avatar::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(0, 255, 255, 0.1) 0%, transparent 70%);
  animation: cyber-pulse 3s ease-in-out infinite;
}

.cyberpunk-mode .user-avatar img,
.cyberpunk-mode .user-avatar-icon {
  position: relative;
  z-index: 1;
}

/* === CYBERPUNK USER NAME === */
.cyberpunk-mode .user-name {
  font-family: 'Orbitron', monospace;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  text-shadow: var(--cyber-glow-blue);
}

/* === CYBERPUNK BALANCE === */
.cyberpunk-mode .balance-info {
  background: rgba(0, 0, 0, 0.7);
  border: var(--cyber-border-glow);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.cyberpunk-mode .balance-info:hover {
  border-color: var(--cyber-neon-blue);
  box-shadow: var(--cyber-glow-blue);
  transform: translateY(-2px);
}

.cyberpunk-mode .balance-amount {
  font-family: 'Orbitron', monospace;
  font-weight: 700;
  color: var(--cyber-neon-green);
  text-shadow: var(--cyber-glow-green);
}

.cyberpunk-mode .balance-currency {
  font-family: 'Orbitron', monospace;
  color: rgba(255, 255, 255, 0.7);
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* === CYBERPUNK HEADERS === */
.cyberpunk-mode h1, 
.cyberpunk-mode h2, 
.cyberpunk-mode h3, 
.cyberpunk-mode h4, 
.cyberpunk-mode h5, 
.cyberpunk-mode h6 {
  font-family: 'Orbitron', monospace;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 2px;
}

.cyberpunk-mode h2 {
  color: var(--cyber-neon-blue);
  text-shadow: var(--cyber-glow-blue);
}

.cyberpunk-mode h3 {
  color: var(--cyber-neon-pink);
  text-shadow: var(--cyber-glow-pink);
}

/* === CYBERPUNK STATUS MESSAGE === */
.cyberpunk-mode .status-message {
  background: var(--cyber-gradient-card);
  border: var(--cyber-border-glow);
  font-family: 'Orbitron', monospace;
  text-transform: uppercase;
  letter-spacing: 1px;
  backdrop-filter: blur(10px);
  box-shadow: var(--cyber-glow-blue);
  position: relative;
  overflow: hidden;
}

.cyberpunk-mode .status-message::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.1), transparent);
  animation: cyber-scan 2s linear infinite;
}

.cyberpunk-mode .status-message.success {
  color: var(--cyber-neon-green);
  text-shadow: var(--cyber-glow-green);
  border-color: var(--cyber-neon-green);
  box-shadow: var(--cyber-glow-green);
}

.cyberpunk-mode .status-message.error {
  color: var(--cyber-neon-pink);
  text-shadow: var(--cyber-glow-pink);
  border-color: var(--cyber-neon-pink);
  box-shadow: var(--cyber-glow-pink);
}

.cyberpunk-mode .status-message.info {
  color: var(--cyber-neon-blue);
  text-shadow: var(--cyber-glow-blue);
  border-color: var(--cyber-neon-blue);
  box-shadow: var(--cyber-glow-blue);
}

/* === CYBERPUNK CARDS === */
.cyberpunk-mode .friends-block,
.cyberpunk-mode .earn-block {
  background: var(--cyber-gradient-card);
  border: var(--cyber-border-glow);
  box-shadow: 
    var(--cyber-glow-blue),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.cyberpunk-mode .friends-block::before,
.cyberpunk-mode .earn-block::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--cyber-gradient-neon);
  opacity: 0.8;
}

.cyberpunk-mode .friends-block::after,
.cyberpunk-mode .earn-block::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: 
    radial-gradient(circle, rgba(0, 255, 255, 0.05) 0%, transparent 70%);
  animation: cyber-pulse 4s ease-in-out infinite;
  pointer-events: none;
}

/* === CYBERPUNK BUTTONS === */
.cyberpunk-mode .action-button {
  font-family: 'Orbitron', monospace;
  text-transform: uppercase;
  letter-spacing: 1px;
  background: var(--cyber-gradient-card);
  border: var(--cyber-border-glow);
  color: var(--cyber-neon-blue);
  text-shadow: var(--cyber-glow-blue);
  box-shadow: var(--cyber-glow-blue);
  position: relative;
  overflow: hidden;
}

.cyberpunk-mode .action-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.cyberpunk-mode .action-button:hover::before {
  left: 100%;
}

.cyberpunk-mode .action-button:hover {
  border-color: var(--cyber-neon-blue);
  box-shadow: var(--cyber-glow-blue);
  transform: translateY(-2px);
}

.cyberpunk-mode .action-button.primary-action {
  background: linear-gradient(135deg, var(--cyber-neon-blue), var(--cyber-neon-purple));
  border-color: var(--cyber-neon-blue);
  color: #000000;
  text-shadow: none;
}

.cyberpunk-mode .action-button.secondary-action {
  background: linear-gradient(135deg, var(--cyber-neon-pink), var(--cyber-neon-orange));
  border-color: var(--cyber-neon-pink);
  color: #000000;
  text-shadow: none;
}

/* === CYBERPUNK COUNTDOWN === */
.cyberpunk-mode .countdown-overlay {
  background: rgba(0, 0, 0, 0.8);
  color: var(--cyber-neon-blue);
  text-shadow: var(--cyber-glow-blue);
  font-family: 'Orbitron', monospace;
  backdrop-filter: blur(5px);
  font-size: 24px;
}

/* === CYBERPUNK NAVIGATION === */
.cyberpunk-mode .app-nav {
  background: var(--cyber-gradient-card);
  border-top: var(--cyber-border-glow);
  backdrop-filter: blur(20px);
  box-shadow: 
    0 -5px 20px rgba(0, 0, 0, 0.5),
    var(--cyber-glow-blue);
}

.cyberpunk-mode .app-nav::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--cyber-gradient-neon);
  opacity: 0.8;
}

.cyberpunk-mode .nav-button {
  color: rgba(255, 255, 255, 0.6);
  font-family: 'Orbitron', monospace;
  transition: all 0.3s ease;
}

.cyberpunk-mode .nav-button.active {
  background: linear-gradient(135deg, rgba(0, 255, 255, 0.2), rgba(255, 0, 128, 0.2));
  box-shadow: inset var(--cyber-glow-blue);
  color: var(--cyber-neon-blue);
  border-radius: 10px;
  margin: 0 5px;
}

.cyberpunk-mode .nav-button:hover {
  color: var(--cyber-neon-blue);
  transform: translateY(-2px);
}

.cyberpunk-mode .nav-icon {
  filter: drop-shadow(0 0 5px currentColor);
}

.cyberpunk-mode .nav-text {
  text-shadow: 0 0 5px currentColor;
  font-weight: 600;
  letter-spacing: 1px;
}

/* === CYBERPUNK INPUTS === */
.cyberpunk-mode input, 
.cyberpunk-mode select, 
.cyberpunk-mode textarea {
  font-family: 'Rajdhani', sans-serif;
  background: rgba(0, 0, 0, 0.7);
  border: var(--cyber-border-glow);
  color: var(--cyber-neon-blue);
  backdrop-filter: blur(5px);
}

.cyberpunk-mode input:focus, 
.cyberpunk-mode select:focus, 
.cyberpunk-mode textarea:focus {
  border-color: var(--cyber-neon-blue);
  box-shadow: var(--cyber-glow-blue);
  background: rgba(0, 0, 0, 0.9);
}

.cyberpunk-mode input::placeholder {
  color: rgba(255, 255, 255, 0.5);
  font-style: italic;
}

/* === CYBERPUNK REFERRAL AREA === */
.cyberpunk-mode .referral-link-area input[type="text"] {
  background: rgba(0, 0, 0, 0.7);
  border: var(--cyber-border-glow);
  color: var(--cyber-neon-blue);
}

.cyberpunk-mode .referral-link-area .copy-button {
  background: var(--cyber-gradient-card);
  border: var(--cyber-border-glow);
  box-shadow: var(--cyber-glow-blue);
}

.cyberpunk-mode .referral-link-area .copy-button:hover {
  border-color: var(--cyber-neon-blue);
  box-shadow: var(--cyber-glow-blue);
  transform: translateY(-2px);
}

/* === CYBERPUNK ANIMATIONS === */
@keyframes cyber-pulse {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.1;
    transform: scale(1.1);
  }
}

@keyframes cyber-scan {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100vw);
  }
}

@keyframes cyber-flicker {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

@keyframes cyber-glow-pulse {
  0%, 100% {
    box-shadow: var(--cyber-glow-blue);
  }
  50% {
    box-shadow:
      var(--cyber-glow-blue),
      0 0 30px rgba(0, 255, 255, 0.8);
  }
}

/* === CYBERPUNK SCROLLBAR === */
.cyberpunk-mode .app-main::-webkit-scrollbar,
.cyberpunk-mode .app-section::-webkit-scrollbar {
  width: 6px;
}

.cyberpunk-mode .app-main::-webkit-scrollbar-track,
.cyberpunk-mode .app-section::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 3px;
}

.cyberpunk-mode .app-main::-webkit-scrollbar-thumb,
.cyberpunk-mode .app-section::-webkit-scrollbar-thumb {
  background: var(--cyber-neon-blue);
  border-radius: 3px;
  box-shadow: var(--cyber-glow-blue);
}

.cyberpunk-mode .app-main::-webkit-scrollbar-thumb:hover,
.cyberpunk-mode .app-section::-webkit-scrollbar-thumb:hover {
  background: var(--cyber-neon-pink);
  box-shadow: var(--cyber-glow-pink);
}

/* === CYBERPUNK RESPONSIVE === */
@media (max-width: 768px) {
  .cyberpunk-mode .user-avatar {
    width: 35px;
    height: 35px;
  }

  .cyberpunk-mode .user-avatar img,
  .cyberpunk-mode .user-avatar-icon {
    width: 20px;
    height: 20px;
  }

  .cyberpunk-mode .user-name {
    font-size: 12px;
    max-width: 120px;
  }

  .cyberpunk-mode .balance-amount {
    font-size: 14px;
  }

  .cyberpunk-mode .balance-currency {
    font-size: 10px;
  }

  .cyberpunk-mode .nav-icon {
    width: 20px;
    height: 20px;
  }

  .cyberpunk-mode .nav-text {
    font-size: 10px;
  }
}

/* === CYBERPUNK ACTIVATION === */
/* Класс для активации киберпанк режима */
.cyberpunk-toggle {
  position: fixed;
  top: 70px;
  right: 20px;
  z-index: 1000;
  background: var(--cyber-gradient-card);
  border: var(--cyber-border-glow);
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: var(--cyber-glow-blue);
  backdrop-filter: blur(10px);
}

.cyberpunk-toggle:hover {
  transform: scale(1.1);
  box-shadow: var(--cyber-glow-pink);
}

.cyberpunk-toggle::before {
  content: '🔥';
  font-size: 20px;
  animation: cyber-flicker 2s ease-in-out infinite;
}

/* === CYBERPUNK PARTICLES EFFECT === */
.cyber-particles {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
  overflow: hidden;
}

.cyber-particle {
  position: absolute;
  width: 2px;
  height: 2px;
  background: var(--cyber-neon-blue);
  border-radius: 50%;
  animation: cyber-float 10s linear infinite;
  box-shadow: 0 0 6px var(--cyber-neon-blue);
}

.cyber-particle:nth-child(2n) {
  background: var(--cyber-neon-pink);
  box-shadow: 0 0 6px var(--cyber-neon-pink);
  animation-duration: 12s;
}

.cyber-particle:nth-child(3n) {
  background: var(--cyber-neon-green);
  box-shadow: 0 0 6px var(--cyber-neon-green);
  animation-duration: 8s;
}

@keyframes cyber-float {
  0% {
    transform: translateY(100vh) translateX(0);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) translateX(100px);
    opacity: 0;
  }
}
