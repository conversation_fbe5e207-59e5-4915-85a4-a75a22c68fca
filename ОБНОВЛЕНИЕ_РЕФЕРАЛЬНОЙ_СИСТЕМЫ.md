# 🔄 Обновление реферальной системы для нового бота

## ❌ ПРОБЛЕМА:
Реферальные ссылки все еще указывают на старого бота `@applanza_test_bot`, а должны указывать на нового `@uniqpaid_paid_bot`.

## ✅ РЕШЕНИЕ:

### 1. **Обновите файлы на сервере:**

Загрузите эти обновленные файлы в папку `app.uniqpaid.com/test2/`:

#### `bot/config.php`:
```php
<?php
/**
 * config.php
 * Конфигурация Telegram бота
 */

// Токен бота
define('BOT_TOKEN', '**********************************************');

// URL для webhook (замените на ваш домен)
define('WEBHOOK_URL', 'https://app.uniqpaid.com/test2/bot/webhook.php');

// URL мини-приложения
define('WEBAPP_URL', 'https://app.uniqpaid.com/test2/');

// Настройки базы данных (используем тот же файл, что и мини-приложение)
define('USER_DATA_FILE', __DIR__ . '/../api/user_data.json');

// Настройки монет
define('COINS_PER_VIEW', 10);
define('COIN_VALUE_USD', 0.01); // 1 монета = 0.01 USD

// Настройки реферальной программы
define('REFERRAL_BONUS_PERCENT', 10); // 10% от заработка рефералов

// Имя бота для ссылок
define('BOT_USERNAME', 'uniqpaid_paid_bot'); // Имя вашего бота

// API Telegram
define('TELEGRAM_API_URL', 'https://api.telegram.org/bot' . BOT_TOKEN . '/');

// Логирование
define('LOG_FILE', __DIR__ . '/bot.log');

// ... остальные функции остаются без изменений
?>
```

#### `api/config.php`:
```php
<?php
// api/config.php

// !!! ТВОЙ ТОКЕН БОТА !!! (Обновленный токен)
define('TELEGRAM_BOT_TOKEN', '**********************************************'); // <-- Новый токен бота

// ... остальные настройки остаются без изменений
?>
```

#### `main.js` (уже обновлен):
```javascript
const BOT_USERNAME = "uniqpaid_paid_bot"; // <-- !!! ИМЯ ВАШЕГО БОТА (БЕЗ @) !!!
```

### 2. **Проверьте обновления:**

После загрузки файлов проверьте:

1. **Откройте мини-приложение:** https://app.uniqpaid.com/test2/
2. **Перейдите в раздел "Друзья"**
3. **Проверьте реферальную ссылку** - должна быть:
   ```
   https://t.me/uniqpaid_paid_bot?start=ВАШЕ_ID
   ```

### 3. **Протестируйте реферальную систему:**

1. **Скопируйте реферальную ссылку** из приложения
2. **Откройте ссылку в новом окне/устройстве**
3. **Проверьте, что открывается бот** `@uniqpaid_paid_bot`
4. **Отправьте `/start`** в боте
5. **Проверьте статистику рефералов** в приложении

### 4. **Настройте мини-приложение в @BotFather:**

1. Отправьте `/newapp` в @BotFather
2. Выберите бота: `@uniqpaid_paid_bot`
3. Название: `UniQPaid`
4. Описание: `Зарабатывайте монеты за просмотр рекламы`
5. URL: `https://app.uniqpaid.com/test2/`

## 🧪 ТЕСТИРОВАНИЕ:

### Правильная реферальная ссылка:
```
https://t.me/uniqpaid_paid_bot?start=5880288830
```

### Неправильная (старая) ссылка:
```
https://t.me/applanza_test_bot?start=5880288830
```

## 📋 ЧЕКЛИСТ ОБНОВЛЕНИЯ:

- [ ] Загружен `bot/config.php` с новым токеном
- [ ] Загружен `api/config.php` с новым токеном  
- [ ] Проверен `main.js` (должен быть `uniqpaid_paid_bot`)
- [ ] Webhook установлен для нового бота
- [ ] Команды установлены для нового бота
- [ ] Мини-приложение настроено в @BotFather
- [ ] Реферальные ссылки указывают на нового бота
- [ ] Протестирована регистрация рефералов

## 🎯 РЕЗУЛЬТАТ:

После обновления:
- ✅ Реферальные ссылки будут указывать на `@uniqpaid_paid_bot`
- ✅ Новые пользователи будут регистрироваться через нового бота
- ✅ Статистика рефералов будет работать корректно
- ✅ Все функции бота будут работать с новым токеном

## 🚀 ГОТОВО!

После выполнения всех шагов реферальная система будет работать идеально с новым ботом `@uniqpaid_paid_bot`!
