<?php
/**
 * Тест автоматической конвертации валют NOWPayments API
 */

// Подключаем необходимые файлы
require_once __DIR__ . '/api/config.php';
require_once __DIR__ . '/api/NOWPaymentsAPI.php';

echo "=== ТЕСТ АВТОМАТИЧЕСКОЙ КОНВЕРТАЦИИ NOWPayments API ===\n";
echo "Время: " . date('Y-m-d H:i:s') . "\n\n";

// Создаем экземпляр API
$api = new NOWPaymentsAPI(
    NOWPAYMENTS_API_KEY,
    NOWPAYMENTS_PUBLIC_KEY,
    NOWPAYMENTS_IPN_SECRET,
    NOWPAYMENTS_API_URL
);

// Тест 1: Проверка текущего баланса
echo "1️⃣ Проверка текущего баланса...\n";
$balance = $api->getAccountBalance();

if ($balance) {
    echo "✅ Баланс получен:\n";
    foreach ($balance as $currency => $data) {
        $amount = $data['amount'] ?? 0;
        $pending = $data['pendingAmount'] ?? 0;
        $available = $amount - $pending;
        echo "   {$currency}: {$amount} (доступно: {$available})\n";
    }
} else {
    echo "❌ Не удалось получить баланс\n";
    exit(1);
}
echo "\n";

// Тест 2: Попытка создать выплату в USDT TRC20 (которой нет в наличии)
echo "2️⃣ Тест автоконвертации для USDT TRC20...\n";

$testAddress = 'TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK';
$testCurrency = 'usdttrc20';
$testAmount = 0.00004; // Небольшая сумма для теста

echo "   Целевой адрес: {$testAddress}\n";
echo "   Целевая валюта: {$testCurrency}\n";
echo "   Целевая сумма: {$testAmount}\n\n";

echo "   Проверяем возможность автоконвертации...\n";

$result = $api->createPayoutWithAutoConversion($testAddress, $testCurrency, $testAmount);

if (isset($result['error'])) {
    echo "❌ Автоконвертация не удалась:\n";
    echo "   Код: {$result['code']}\n";
    echo "   Сообщение: {$result['message']}\n";
} elseif (isset($result['id'])) {
    echo "✅ Автоконвертация успешна!\n";
    echo "   ID выплаты: {$result['id']}\n";
    
    if (isset($result['auto_conversion'])) {
        $autoConv = $result['auto_conversion'];
        echo "   📄 Информация о конвертации:\n";
        echo "      Запрошено: {$autoConv['original_request']['amount']} {$autoConv['original_request']['currency']}\n";
        echo "      Фактически: {$autoConv['actual_payout']['amount']} {$autoConv['actual_payout']['currency']}\n";
        echo "      Описание: {$autoConv['conversion_info']}\n";
        
        // Проверяем статус выплаты
        echo "\n   🔍 Проверка статуса выплаты...\n";
        sleep(2);
        
        $status = $api->getPayoutStatus($result['id']);
        if ($status) {
            echo "   ✅ Статус получен:\n";
            echo "      ID: " . ($status['id'] ?? 'неизвестен') . "\n";
            echo "      Статус: " . ($status['withdrawals'][0]['status'] ?? 'неизвестен') . "\n";
            echo "      Валюта: " . ($status['withdrawals'][0]['currency'] ?? 'неизвестна') . "\n";
            echo "      Сумма: " . ($status['withdrawals'][0]['amount'] ?? 'неизвестна') . "\n";
        } else {
            echo "   ⚠️  Не удалось получить статус\n";
        }
    } else {
        echo "   ℹ️  Прямая выплата (без конвертации)\n";
    }
} else {
    echo "❌ Неожиданный ответ:\n";
    echo "   " . json_encode($result, JSON_PRETTY_PRINT) . "\n";
}
echo "\n";

// Тест 3: Проверка курсов конвертации
echo "3️⃣ Проверка курсов конвертации...\n";

// Находим валюту с балансом
$availableCurrency = $api->findCurrencyWithBalance(0.00001);

if ($availableCurrency) {
    $sourceCurrency = $availableCurrency['currency'];
    $sourceAmount = min($availableCurrency['available'], 0.00001);
    
    echo "   Исходная валюта: {$sourceCurrency}\n";
    echo "   Доступная сумма: {$sourceAmount}\n\n";
    
    // Проверяем конвертацию в разные валюты
    $targetCurrencies = ['usdttrc20', 'usdterc20', 'btc', 'eth', 'trx'];
    
    foreach ($targetCurrencies as $targetCurrency) {
        if ($targetCurrency === $sourceCurrency) continue;
        
        echo "   💱 {$sourceCurrency} -> {$targetCurrency}: ";
        
        $estimate = $api->getEstimateAmount($sourceAmount, $sourceCurrency, $targetCurrency);
        
        if ($estimate && isset($estimate['estimated_amount'])) {
            $convertedAmount = $estimate['estimated_amount'];
            $rate = $convertedAmount / $sourceAmount;
            echo "{$sourceAmount} -> {$convertedAmount} (курс: {$rate})\n";
        } else {
            echo "не удалось получить курс\n";
        }
    }
} else {
    echo "   ⚠️  Нет валют с достаточным балансом для тестирования курсов\n";
}
echo "\n";

// Тест 4: Симуляция реального запроса пользователя
echo "4️⃣ Симуляция реального запроса пользователя...\n";

echo "   Пользователь хочет вывести 4 монеты в USDT TRC20\n";
echo "   Конвертация: 4 монеты = 0.04 USD\n";

// Получаем оценку суммы в USDT TRC20
$usdAmount = 0.04;
$estimate = $api->getEstimateAmount($usdAmount, 'usd', 'usdttrc20');

if ($estimate && isset($estimate['estimated_amount'])) {
    $usdtAmount = $estimate['estimated_amount'];
    echo "   Требуется: {$usdtAmount} USDT TRC20\n\n";
    
    // Пробуем создать выплату с автоконвертацией
    echo "   🔄 Попытка создания выплаты с автоконвертацией...\n";
    
    $userResult = $api->createPayoutWithAutoConversion(
        'TTXpztSD9SqzpzoBVn1bWfBg6AXyBGHGeK',
        'usdttrc20',
        $usdtAmount
    );
    
    if (isset($userResult['error'])) {
        echo "   ❌ Ошибка: {$userResult['message']}\n";
    } elseif (isset($userResult['id'])) {
        echo "   ✅ Выплата создана!\n";
        echo "   ID: {$userResult['id']}\n";
        
        if (isset($userResult['auto_conversion'])) {
            echo "   🔄 Использована автоконвертация\n";
        } else {
            echo "   ➡️  Прямая выплата\n";
        }
    }
} else {
    echo "   ❌ Не удалось получить оценку суммы\n";
}
echo "\n";

echo "=== ТЕСТ ЗАВЕРШЕН ===\n";
echo "Время: " . date('Y-m-d H:i:s') . "\n\n";

echo "📋 ВЫВОДЫ:\n";
echo "1. Автоматическая конвертация позволяет использовать доступные валюты\n";
echo "2. Система автоматически находит лучший курс конвертации\n";
echo "3. Пользователи получают выплаты даже при отсутствии запрашиваемой валюты\n";
echo "4. Информация о конвертации передается пользователю\n";
echo "5. Улучшен пользовательский опыт - больше никаких отказов из-за баланса!\n";
?>
