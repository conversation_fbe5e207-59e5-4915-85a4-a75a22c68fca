<?php
/**
 * api/admin/index.php
 * Главная страница административной панели
 */

// Включаем логирование
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../error.log');
error_reporting(E_ALL);

// Подключаем файл с функциями аутентификации и авторизации
require_once __DIR__ . '/auth.php';

// Проверяем аутентификацию
session_start();
if (!isAuthenticated()) {
    // Если пользователь не аутентифицирован, перенаправляем на страницу входа
    header('Location: login.php');
    exit;
}

// --- Подключение зависимостей с проверкой ---
if (!(@require_once __DIR__ . '/../config.php')) { 
    http_response_code(500); 
    error_log('FATAL: config.php not found in admin/index.php'); 
    die('Ошибка: Не удалось загрузить config.php'); 
}
if (!(@require_once __DIR__ . '/../db_mock.php')) { 
    http_response_code(500); 
    error_log('FATAL: db_mock.php not found in admin/index.php'); 
    die('Ошибка: Не удалось загрузить db_mock.php'); 
}
if (!(@require_once __DIR__ . '/../security.php')) { 
    http_response_code(500); 
    error_log('FATAL: security.php not found in admin/index.php'); 
    die('Ошибка: Не удалось загрузить security.php'); 
}
// --- Конец проверки зависимостей ---

// Загрузка данных пользователей
$userData = loadUserData();
if (!is_array($userData)) {
    die('Ошибка: Не удалось загрузить данные пользователей');
}

// Получение статистики
$totalUsers = count($userData);
$totalBalance = 0;
$totalReferrals = 0;
$totalWithdrawals = 0;
$totalWithdrawalAmount = 0;
$totalAdViews = 0;
$blockedUsers = 0;
$suspiciousUsers = 0;

foreach ($userData as $userId => $user) {
    $totalBalance += $user['balance'] ?? 0;
    $totalReferrals += $user['referrals_count'] ?? 0;
    
    if (isset($user['withdrawals']) && is_array($user['withdrawals'])) {
        $totalWithdrawals += count($user['withdrawals']);
        foreach ($user['withdrawals'] as $withdrawal) {
            $totalWithdrawalAmount += $withdrawal['coins_amount'] ?? 0;
        }
    }
    
    if (isset($user['ad_views_log']) && is_array($user['ad_views_log'])) {
        $totalAdViews += count($user['ad_views_log']);
    }
    
    if (isset($user['blocked']) && $user['blocked']) {
        $blockedUsers++;
    }
    
    if (isset($user['suspicious_activity']) && $user['suspicious_activity'] > 0) {
        $suspiciousUsers++;
    }
}

// Получение последних действий из журнала аудита
$auditLog = [];
$auditLogFile = __DIR__ . '/../audit.log';
if (file_exists($auditLogFile)) {
    $auditLog = array_slice(array_reverse(file($auditLogFile)), 0, 10);
}

// Обработка действий
$actionMessage = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    switch ($_POST['action']) {
        case 'clear_audit_log':
            if (file_exists($auditLogFile)) {
                file_put_contents($auditLogFile, '');
                $actionMessage = 'Журнал аудита успешно очищен';
                $auditLog = [];
            }
            break;
    }
}

// Получение активной страницы
$page = isset($_GET['page']) ? $_GET['page'] : 'dashboard';

// Заголовок страницы
$pageTitle = 'Панель управления';
switch ($page) {
    case 'users':
        $pageTitle = 'Управление пользователями';
        break;
    case 'stats':
        $pageTitle = 'Статистика';
        break;
    case 'settings':
        $pageTitle = 'Настройки';
        break;
    case 'security':
        $pageTitle = 'Безопасность';
        break;
    case 'balance':
        $pageTitle = 'Баланс NOWPayments';
        break;
}

// Подключаем шаблон заголовка
include 'templates/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Боковое меню -->
        <nav id="sidebar" class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
            <div class="position-sticky pt-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link <?php echo $page === 'dashboard' ? 'active' : ''; ?>" href="index.php">
                            <i class="bi bi-speedometer2 me-2"></i>
                            Панель управления
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $page === 'users' ? 'active' : ''; ?>" href="users.php">
                            <i class="bi bi-people me-2"></i>
                            Пользователи
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $page === 'stats' ? 'active' : ''; ?>" href="stats.php">
                            <i class="bi bi-bar-chart me-2"></i>
                            Статистика
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $page === 'security' ? 'active' : ''; ?>" href="security.php">
                            <i class="bi bi-shield-lock me-2"></i>
                            Безопасность
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $page === 'settings' ? 'active' : ''; ?>" href="settings.php">
                            <i class="bi bi-gear me-2"></i>
                            Настройки
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $page === 'balance' ? 'active' : ''; ?>" href="balance.php">
                            <i class="bi bi-wallet2 me-2"></i>
                            Баланс NOWPayments
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="logout.php">
                            <i class="bi bi-box-arrow-right me-2"></i>
                            Выход
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- Основное содержимое -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><?php echo $pageTitle; ?></h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="index.php" class="btn btn-sm btn-outline-secondary">Обновить</a>
                    </div>
                </div>
            </div>

            <?php if (!empty($actionMessage)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo $actionMessage; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <!-- Карточки с основной статистикой -->
            <div class="row">
                <div class="col-md-3 mb-4">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        Пользователей</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $totalUsers; ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-people fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-3 mb-4">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        Общий баланс</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $totalBalance; ?> монет</div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-currency-exchange fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-3 mb-4">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        Просмотров рекламы</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $totalAdViews; ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-eye fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-3 mb-4">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        Выводов средств</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $totalWithdrawals; ?></div>
                                </div>
                                <div class="col-auto">
                                    <i class="bi bi-cash-stack fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Карточка баланса NOWPayments -->
            <div class="row">
                <div class="col-md-12 mb-4">
                    <div class="card border-left-info shadow">
                        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                            <h6 class="m-0 font-weight-bold text-info">💰 Баланс NOWPayments</h6>
                            <a href="balance.php" class="btn btn-sm btn-info">Подробнее</a>
                        </div>
                        <div class="card-body">
                            <?php
                            // Получаем баланс NOWPayments
                            try {
                                require_once __DIR__ . '/../NOWPaymentsAPI.php';
                                $nowApi = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);
                                $nowBalance = $nowApi->getAccountBalance();

                                if ($nowBalance) {
                                    $totalNowUSD = 0;
                                    $balanceCount = 0;

                                    echo '<div class="row">';
                                    foreach ($nowBalance as $currency => $data) {
                                        $amount = is_array($data) ? ($data['amount'] ?? 0) : $data;
                                        if ($amount > 0) {
                                            $balanceCount++;
                                            echo '<div class="col-md-3 mb-2">';
                                            echo '<div class="text-xs font-weight-bold text-uppercase">' . strtoupper($currency) . '</div>';
                                            echo '<div class="h6 mb-0">' . $amount . '</div>';
                                            echo '</div>';

                                            // Конвертируем в USD
                                            try {
                                                $estimate = $nowApi->getEstimateAmount($amount, $currency, 'usd');
                                                if (isset($estimate['estimated_amount'])) {
                                                    $totalNowUSD += $estimate['estimated_amount'];
                                                }
                                            } catch (Exception $e) {
                                                // Игнорируем ошибки конвертации
                                            }
                                        }
                                    }
                                    echo '</div>';

                                    if ($balanceCount > 0) {
                                        echo '<div class="mt-2 pt-2 border-top">';
                                        echo '<div class="text-xs font-weight-bold text-uppercase">Общая стоимость</div>';
                                        echo '<div class="h5 mb-0 text-info">≈ ' . round($totalNowUSD, 2) . ' USD</div>';
                                        echo '</div>';
                                    } else {
                                        echo '<p class="text-muted mb-0">Нет доступного баланса</p>';
                                    }
                                } else {
                                    echo '<p class="text-danger mb-0">Ошибка подключения к NOWPayments API</p>';
                                }
                            } catch (Exception $e) {
                                echo '<p class="text-danger mb-0">Ошибка: ' . htmlspecialchars($e->getMessage()) . '</p>';
                            }
                            ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Дополнительная статистика -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                            <h6 class="m-0 font-weight-bold text-primary">Последние действия</h6>
                            <form method="post">
                                <input type="hidden" name="action" value="clear_audit_log">
                                <button type="submit" class="btn btn-sm btn-outline-danger">Очистить журнал</button>
                            </form>
                        </div>
                        <div class="card-body">
                            <?php if (empty($auditLog)): ?>
                                <p class="text-center">Журнал аудита пуст</p>
                            <?php else: ?>
                                <div style="max-height: 300px; overflow-y: auto;">
                                    <?php foreach ($auditLog as $logEntry): ?>
                                        <div class="log-entry small"><?php echo htmlspecialchars($logEntry); ?></div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Безопасность</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-4">
                                    <div class="card bg-danger text-white shadow">
                                        <div class="card-body">
                                            Заблокировано пользователей
                                            <div class="text-white-50 small"><?php echo $blockedUsers; ?></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-4">
                                    <div class="card bg-warning text-white shadow">
                                        <div class="card-body">
                                            Подозрительная активность
                                            <div class="text-white-50 small"><?php echo $suspiciousUsers; ?></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="text-center">
                                <a href="security.php" class="btn btn-primary">Подробнее</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<?php
// Подключаем шаблон подвала
include 'templates/footer.php';
?>
